/**
 * Game Board Component
 * Main game rendering and interaction component
 */

class GameBoardComponent extends Component {
    constructor(container, props = {}) {
        super(container, props);
        
        this.state = {
            isVisible: true
        };
        
        // Game systems references
        this.gameState = props.gameState;
        this.mapSystem = props.mapSystem;
        this.playerSystem = props.playerSystem;
        this.bombSystem = props.bombSystem;
        this.powerupSystem = props.powerupSystem;
        
        // Bind methods
        this.handleClick = this.handleClick.bind(this);
        this.handleKeyPress = this.handleKeyPress.bind(this);
    }
    
    /**
     * Render game board component
     */
    render() {
        this.element = DOM.createElement('div', {
            className: 'game-board',
            tabindex: '0', // Make focusable for keyboard events
            style: {
                display: this.state.isVisible ? 'block' : 'none',
                outline: 'none' // Remove focus outline
            },
            onClick: this.handleClick,
            onKeyDown: this.handleKeyPress
        });
        
        this.createGameElements();
        return this.element;
    }
    
    /**
     * Create game elements
     */
    createGameElements() {
        if (!this.element) return;
        
        DOM.clear(this.element);
        
        // Create map if available
        if (this.mapSystem && this.gameState.map) {
            this.mapSystem.createMapElement(this.element);
        }
        
        // Create player elements
        if (this.playerSystem) {
            for (const player of this.gameState.getAllPlayers()) {
                this.playerSystem.createPlayerElement(player, this.element);
            }
        }
        
        // Create existing bombs
        if (this.bombSystem) {
            for (const bomb of this.gameState.bombs.values()) {
                if (!bomb.element) {
                    this.bombSystem.createBombElement(bomb);
                }
            }
        }
        
        // Create existing powerups
        if (this.powerupSystem) {
            for (const powerup of this.gameState.powerups.values()) {
                if (!powerup.element) {
                    this.powerupSystem.createPowerupElement(powerup);
                }
            }
        }
    }
    
    /**
     * Handle click events
     */
    handleClick(event) {
        // Focus the game board for keyboard input
        this.element.focus();
        
        // Get click position relative to game board
        const rect = this.element.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // Convert to grid coordinates
        const gridPos = this.gameState.pixelToGrid(x, y);
        
        console.log(`Clicked at pixel (${x}, ${y}) = grid (${gridPos.x}, ${gridPos.y})`);
        
        // Emit click event
        this.emit('boardClick', { x, y, gridX: gridPos.x, gridY: gridPos.y });
    }
    
    /**
     * Handle keyboard events
     */
    handleKeyPress(event) {
        // Prevent default for game keys
        const gameKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'];
        if (gameKeys.includes(event.code)) {
            event.preventDefault();
        }
        
        // Emit key event
        this.emit('keyPress', { code: event.code, key: event.key });
    }
    
    /**
     * Update game board
     */
    updateBoard() {
        // This method can be called to refresh the entire board
        this.createGameElements();
    }
    
    /**
     * Add entity element to board
     */
    addEntity(entity, type) {
        if (!this.element || !entity.element) return;
        
        // Ensure entity element is in the game board
        if (!this.element.contains(entity.element)) {
            this.element.appendChild(entity.element);
        }
        
        // Set appropriate z-index based on type
        switch (type) {
            case 'player':
                entity.element.style.zIndex = '10';
                break;
            case 'bomb':
                entity.element.style.zIndex = '5';
                break;
            case 'powerup':
                entity.element.style.zIndex = '6';
                break;
            case 'explosion':
                entity.element.style.zIndex = '8';
                break;
        }
    }
    
    /**
     * Remove entity element from board
     */
    removeEntity(entity) {
        if (entity.element && this.element && this.element.contains(entity.element)) {
            this.element.removeChild(entity.element);
        }
    }
    
    /**
     * Get board dimensions
     */
    getDimensions() {
        if (!this.element) return { width: 0, height: 0 };
        
        const rect = this.element.getBoundingClientRect();
        return {
            width: rect.width,
            height: rect.height
        };
    }
    
    /**
     * Get board position
     */
    getPosition() {
        if (!this.element) return { x: 0, y: 0 };
        
        const rect = this.element.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top
        };
    }
    
    /**
     * Convert screen coordinates to board coordinates
     */
    screenToBoard(screenX, screenY) {
        const pos = this.getPosition();
        return {
            x: screenX - pos.x,
            y: screenY - pos.y
        };
    }
    
    /**
     * Convert board coordinates to grid coordinates
     */
    boardToGrid(boardX, boardY) {
        return this.gameState.pixelToGrid(boardX, boardY);
    }
    
    /**
     * Show game board
     */
    show() {
        this.setState({ isVisible: true });
    }
    
    /**
     * Hide game board
     */
    hide() {
        this.setState({ isVisible: false });
    }
    
    /**
     * Focus game board for input
     */
    focus() {
        if (this.element) {
            this.element.focus();
        }
    }
    
    /**
     * Update component
     */
    update(prevState) {
        if (prevState.isVisible !== this.state.isVisible) {
            this.element.style.display = this.state.isVisible ? 'block' : 'none';
        }
    }
    
    /**
     * Component mounted
     */
    onMounted() {
        console.log('Game board component mounted');
        
        // Focus the board for keyboard input
        setTimeout(() => this.focus(), 100);
        
        // Listen for game state events
        if (this.gameState) {
            this.gameState.on('playerAdded', (player) => {
                if (this.playerSystem) {
                    this.playerSystem.createPlayerElement(player, this.element);
                }
            });
            
            this.gameState.on('bombAdded', (bomb) => {
                this.addEntity(bomb, 'bomb');
            });
            
            this.gameState.on('powerupAdded', (powerup) => {
                this.addEntity(powerup, 'powerup');
            });
            
            this.gameState.on('explosionAdded', (explosion) => {
                this.addEntity(explosion, 'explosion');
            });
        }
    }
    
    /**
     * Component unmounted
     */
    onUnmounted() {
        console.log('Game board component unmounted');
    }
    
    /**
     * Event system methods
     */
    emit(event, data) {
        if (this.props.onEvent) {
            this.props.onEvent(event, data);
        }
    }
}

// Export for use in other modules
window.GameBoardComponent = GameBoardComponent;
