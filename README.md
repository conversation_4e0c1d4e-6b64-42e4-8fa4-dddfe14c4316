# Bomberman DOM

A multiplayer Bomberman game built with a custom DOM framework, featuring real-time multiplayer gameplay, WebSocket communication, and 60fps performance optimization.

## Features

### Core Gameplay
- **Multiplayer Support**: 2-4 players can join and battle
- **Real-time Gameplay**: WebSocket-based synchronization
- **Classic Mechanics**: Bomb placement, explosions, block destruction
- **Power-ups**: Bomb capacity, explosion range, and speed boosts
- **Lives System**: Each player starts with 3 lives
- **Corner Spawning**: Players start in map corners for fair gameplay

### Technical Features
- **Custom DOM Framework**: Built without Canvas or WebGL
- **60fps Performance**: Optimized game loop with requestAnimationFrame
- **Performance Monitoring**: Real-time FPS and performance tracking
- **Responsive Design**: Works on different screen sizes
- **Chat System**: Real-time player communication
- **Lobby System**: Nickname entry and player waiting room

### Game Mechanics
- **Map Generation**: Random block placement with fixed walls
- **Safe Zones**: Protected areas around starting positions
- **Chain Reactions**: Bombs can trigger other bombs
- **Collision Detection**: Precise player and bomb collision
- **Power-up Spawning**: Random power-ups when blocks are destroyed

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bomberman-dom
```

2. Install Go dependencies:
```bash
go mod tidy
```

3. Start the server:
```bash
go run main.go
```

4. Open your browser and navigate to:
```
http://localhost:8080
```

## How to Play

### Controls
- **Arrow Keys**: Move your player
- **Spacebar**: Place a bomb
- **F1**: Toggle performance monitor
- **F5**: Refresh the game

### Game Flow
1. Enter your nickname in the lobby
2. Wait for other players to join (2-4 players)
3. Game starts automatically when enough players join
4. Use bombs to destroy blocks and eliminate opponents
5. Collect power-ups to become stronger
6. Last player standing wins!

### Power-ups
- **💣 Bomb**: Increases bomb capacity by 1
- **🔥 Flame**: Increases explosion range by 1
- **⚡ Speed**: Increases movement speed

## Architecture

### Mini Framework
The game is built on a custom DOM framework consisting of:

- **DOM Utilities** (`framework/dom.js`): Efficient DOM manipulation
- **Component System** (`framework/component.js`): Reusable UI components
- **Game Engine** (`framework/game-engine.js`): 60fps game loop and performance monitoring

### Game Systems
- **Player System** (`game/systems/player.js`): Player movement and collision
- **Map System** (`game/systems/map.js`): Map generation and block management
- **Bomb System** (`game/systems/bomb.js`): Bomb placement and explosions
- **Powerup System** (`game/systems/powerup.js`): Power-up spawning and collection

### Networking
- **WebSocket Server** (`main.go`): Go-based multiplayer game server
- **WebSocket Client** (`game/network/websocket-client.js`): Client-side networking

### UI Components
- **Lobby Component** (`game/components/lobby.js`): Player lobby and waiting room
- **Chat Component** (`game/components/chat.js`): Real-time chat system
- **HUD Component** (`game/components/hud.js`): Game interface and player stats
- **Game Board Component** (`game/components/game-board.js`): Main game rendering

## Performance

The game is optimized for 60fps performance:

- **Efficient DOM Updates**: Minimal DOM manipulation using transforms
- **Performance Monitoring**: Real-time FPS tracking and warnings
- **Optimized Game Loop**: Separate update and render phases
- **Memory Management**: Proper cleanup of game entities
- **Network Throttling**: Optimized network update frequency

## Development

### Project Structure
```
bomberman-dom/
├── framework/          # Custom DOM framework
├── game/
│   ├── core/          # Core game systems
│   ├── systems/       # Game logic systems
│   ├── components/    # UI components
│   └── network/       # Networking code
├── styles/            # CSS styles
├── main.go            # Go WebSocket server
├── go.mod             # Go module file
└── index.html         # Main HTML file
```

### Adding Features
1. **New Power-ups**: Add to `PowerupSystem.powerupTypes`
2. **Game Modes**: Extend the lobby and game state management
3. **AI Players**: Implement in a new AI system
4. **Visual Effects**: Add CSS animations and particle effects

### Performance Guidelines
- Use `transform` for positioning instead of `left/top`
- Batch DOM updates when possible
- Monitor performance with the built-in performance monitor
- Keep entity counts reasonable for 60fps

## Browser Support

- Modern browsers with WebSocket support
- Chrome, Firefox, Safari, Edge
- Mobile browsers (with touch controls)

## License

MIT License - feel free to use and modify for your projects!

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Troubleshooting

### Common Issues
- **Port in use**: Change the port in `server/server.js`
- **WebSocket connection failed**: Check firewall settings
- **Low FPS**: Check performance monitor and reduce entity count
- **Players not syncing**: Check network connection and server logs

### Debug Tools
- Press **F1** to toggle performance monitor
- Check browser console for error messages
- Monitor network tab for WebSocket messages
- Use the chat system to test connectivity
