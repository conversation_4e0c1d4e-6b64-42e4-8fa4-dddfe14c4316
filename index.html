<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <!-- Game will be rendered here -->
    </div>
    
    <!-- Mini Framework -->
    <script src="framework/dom.js"></script>
    <script src="framework/component.js"></script>
    <script src="framework/game-engine.js"></script>
    
    <!-- Game Core -->
    <script src="game/core/game-state.js"></script>
    <script src="game/core/performance.js"></script>
    <script src="game/core/input.js"></script>

    <!-- Game Systems -->
    <script src="game/systems/player.js"></script>
    <script src="game/systems/map.js"></script>
    <script src="game/systems/bomb.js"></script>
    <script src="game/systems/powerup.js"></script>

    <!-- Game Components -->
    <script src="game/components/lobby.js"></script>
    <script src="game/components/game-board.js"></script>
    <script src="game/components/chat.js"></script>
    <script src="game/components/hud.js"></script>

    <!-- Networking -->
    <script src="game/network/websocket-client.js"></script>
    
    <!-- Main Game -->
    <script src="game/main.js"></script>
</body>
</html>
