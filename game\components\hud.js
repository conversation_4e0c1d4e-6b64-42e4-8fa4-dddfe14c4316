/**
 * HUD Component
 * Displays game UI including lives, powerups, and player info
 */

class HUDComponent extends Component {
    constructor(container, props = {}) {
        super(container, props);
        
        this.state = {
            currentPlayer: null,
            players: [],
            gameTime: 0,
            isVisible: true
        };
        
        // Bind methods
        this.updatePlayerInfo = this.updatePlayerInfo.bind(this);
        this.updateGameTime = this.updateGameTime.bind(this);
        this.formatTime = this.formatTime.bind(this);
    }
    
    /**
     * Render HUD component
     */
    render() {
        this.element = DOM.createElement('div', {
            className: 'hud',
            style: {
                display: this.state.isVisible ? 'flex' : 'none'
            }
        });
        
        this.createHUDElements();
        return this.element;
    }
    
    /**
     * Create HUD UI elements
     */
    createHUDElements() {
        if (!this.element) return;
        
        DOM.clear(this.element);
        
        // Left side - Current player info
        this.createPlayerInfo();
        
        // Center - Game info
        this.createGameInfo();
        
        // Right side - All players status
        this.createPlayersStatus();
    }
    
    /**
     * Create current player info section
     */
    createPlayerInfo() {
        const playerInfoContainer = DOM.createElement('div', {
            className: 'player-info',
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: '15px'
            }
        });
        
        if (this.state.currentPlayer) {
            const player = this.state.currentPlayer;
            
            // Player name and color indicator
            const nameElement = DOM.createElement('div', {
                className: 'player-name',
                style: {
                    fontWeight: 'bold',
                    color: this.getPlayerColor(player.id)
                }
            }, [player.nickname]);
            
            playerInfoContainer.appendChild(nameElement);
            
            // Lives display
            const livesContainer = DOM.createElement('div', {
                className: 'lives-container',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px'
                }
            });
            
            const livesLabel = DOM.createElement('span', {}, ['Lives:']);
            livesContainer.appendChild(livesLabel);
            
            for (let i = 0; i < player.lives; i++) {
                const heart = DOM.createElement('span', {
                    className: 'life-heart',
                    style: {
                        color: '#ff4444',
                        fontSize: '16px'
                    }
                }, ['♥']);
                livesContainer.appendChild(heart);
            }
            
            playerInfoContainer.appendChild(livesContainer);
            
            // Powerups display
            const powerupsContainer = DOM.createElement('div', {
                className: 'powerups-container',
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px'
                }
            });
            
            // Bomb powerup
            const bombPowerup = DOM.createElement('div', {
                className: 'powerup-display',
                title: `Bomb Capacity: ${player.powerups.bombs}`,
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '3px',
                    padding: '2px 6px',
                    backgroundColor: '#ff0000',
                    borderRadius: '3px',
                    fontSize: '12px'
                }
            }, ['💣', player.powerups.bombs.toString()]);
            
            powerupsContainer.appendChild(bombPowerup);
            
            // Flame powerup
            const flamePowerup = DOM.createElement('div', {
                className: 'powerup-display',
                title: `Explosion Range: ${player.powerups.flames}`,
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '3px',
                    padding: '2px 6px',
                    backgroundColor: '#ff6600',
                    borderRadius: '3px',
                    fontSize: '12px'
                }
            }, ['🔥', player.powerups.flames.toString()]);
            
            powerupsContainer.appendChild(flamePowerup);
            
            // Speed powerup
            const speedPowerup = DOM.createElement('div', {
                className: 'powerup-display',
                title: `Speed Level: ${player.powerups.speed}`,
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '3px',
                    padding: '2px 6px',
                    backgroundColor: '#00ff00',
                    borderRadius: '3px',
                    fontSize: '12px'
                }
            }, ['⚡', player.powerups.speed.toString()]);
            
            powerupsContainer.appendChild(speedPowerup);
            
            playerInfoContainer.appendChild(powerupsContainer);
        }
        
        this.element.appendChild(playerInfoContainer);
    }
    
    /**
     * Create game info section
     */
    createGameInfo() {
        const gameInfoContainer = DOM.createElement('div', {
            className: 'game-info',
            style: {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '5px'
            }
        });
        
        // Game title
        const title = DOM.createElement('div', {
            className: 'game-title',
            style: {
                fontWeight: 'bold',
                fontSize: '16px',
                color: '#ff6600'
            }
        }, ['BOMBERMAN']);
        
        gameInfoContainer.appendChild(title);
        
        // Game time
        const timeDisplay = DOM.createElement('div', {
            className: 'game-time',
            style: {
                fontSize: '12px',
                color: '#ccc'
            }
        }, [this.formatTime(this.state.gameTime)]);
        
        gameInfoContainer.appendChild(timeDisplay);
        
        this.element.appendChild(gameInfoContainer);
    }
    
    /**
     * Create players status section
     */
    createPlayersStatus() {
        const playersContainer = DOM.createElement('div', {
            className: 'players-status',
            style: {
                display: 'flex',
                alignItems: 'center',
                gap: '10px'
            }
        });
        
        const playersLabel = DOM.createElement('span', {
            style: { fontSize: '12px', color: '#ccc' }
        }, ['Players:']);
        
        playersContainer.appendChild(playersLabel);
        
        // Display all players
        this.state.players.forEach(player => {
            const playerStatus = DOM.createElement('div', {
                className: `player-status ${player.isAlive ? 'alive' : 'dead'}`,
                title: `${player.nickname} - ${player.isAlive ? 'Alive' : 'Dead'}`,
                style: {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '3px',
                    padding: '2px 6px',
                    backgroundColor: player.isAlive ? this.getPlayerColor(player.id) : '#666',
                    borderRadius: '3px',
                    fontSize: '10px',
                    opacity: player.isAlive ? '1' : '0.5'
                }
            });
            
            const statusIcon = DOM.createElement('span', {}, [player.isAlive ? '●' : '✕']);
            const playerName = DOM.createElement('span', {}, [player.nickname]);
            
            playerStatus.appendChild(statusIcon);
            playerStatus.appendChild(playerName);
            playersContainer.appendChild(playerStatus);
        });
        
        this.element.appendChild(playersContainer);
    }
    
    /**
     * Get player color based on ID
     */
    getPlayerColor(playerId) {
        const colors = ['#ff4444', '#4444ff', '#44ff44', '#ffff44'];
        // Simple hash to get consistent color
        let hash = 0;
        for (let i = 0; i < playerId.length; i++) {
            hash = playerId.charCodeAt(i) + ((hash << 5) - hash);
        }
        return colors[Math.abs(hash) % colors.length];
    }
    
    /**
     * Format time display
     */
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    /**
     * Update current player info
     */
    updatePlayerInfo(player) {
        this.setState({ currentPlayer: player });
    }
    
    /**
     * Update all players
     */
    updatePlayers(players) {
        this.setState({ players: Array.isArray(players) ? players : Array.from(players.values()) });
    }
    
    /**
     * Update game time
     */
    updateGameTime(gameTime) {
        this.setState({ gameTime });
    }
    
    /**
     * Show HUD
     */
    show() {
        this.setState({ isVisible: true });
    }
    
    /**
     * Hide HUD
     */
    hide() {
        this.setState({ isVisible: false });
    }
    
    /**
     * Toggle HUD visibility
     */
    toggle() {
        this.setState({ isVisible: !this.state.isVisible });
    }
    
    /**
     * Update component
     */
    update(prevState) {
        if (prevState.isVisible !== this.state.isVisible) {
            this.element.style.display = this.state.isVisible ? 'flex' : 'none';
        }
        
        // Re-render if any data changed
        if (prevState.currentPlayer !== this.state.currentPlayer ||
            prevState.players !== this.state.players ||
            prevState.gameTime !== this.state.gameTime) {
            this.createHUDElements();
        }
    }
    
    /**
     * Component mounted
     */
    onMounted() {
        console.log('HUD component mounted');
    }
    
    /**
     * Component unmounted
     */
    onUnmounted() {
        console.log('HUD component unmounted');
    }
}

// Export for use in other modules
window.HUDComponent = HUDComponent;
