/**
 * Bomberman DOM - Main Game
 * Entry point and game initialization
 */

console.log('Loading BombermanGame class...');

class BombermanGame {
    constructor() {
        // Core systems
        this.gameEngine = new GameEngine();
        this.gameState = new GameState();
        this.inputManager = new InputManager();
        this.performanceMonitor = new PerformanceMonitor();
        this.networkClient = new WebSocketClient();
        
        // Game systems
        this.playerSystem = null;
        this.mapSystem = null;
        this.bombSystem = null;
        this.powerupSystem = null;

        // UI components
        this.lobbyComponent = null;
        this.chatComponent = null;
        this.hudComponent = null;
        this.currentScreen = 'lobby';

        // Game timing
        this.gameStartTime = null;
        
        // Game container
        this.container = null;
        
        // Bind methods
        this.init = this.init.bind(this);
        this.update = this.update.bind(this);
        this.render = this.render.bind(this);
        this.handleLobbyEvent = this.handleLobbyEvent.bind(this);
        this.startGame = this.startGame.bind(this);
        this.resetGame = this.resetGame.bind(this);
    }
    
    /**
     * Initialize the game
     */
    init() {
        console.log('Initializing Bomberman DOM...');
        
        // Get game container
        this.container = DOM.query('#app');
        if (!this.container) {
            console.error('Game container #app not found');
            return;
        }
        
        // Initialize core systems
        this.performanceMonitor.init();
        
        // Initialize game systems
        this.playerSystem = new PlayerSystem(this.gameState, this.inputManager, this.networkClient);
        this.mapSystem = new MapSystem(this.gameState);
        this.bombSystem = new BombSystem(this.gameState, this.mapSystem, this.playerSystem);
        this.powerupSystem = new PowerupSystem(this.gameState, this.playerSystem);

        this.playerSystem.init();
        this.mapSystem.init();
        this.bombSystem.init();
        this.powerupSystem.init();
        
        // Setup game engine callbacks
        this.gameEngine.onUpdate(this.update);
        this.gameEngine.onRender(this.render);
        this.gameEngine.onFPS((data) => {
            this.performanceMonitor.update(
                data.frameTime,
                data.updateTime,
                data.renderTime,
                this.getEntityCount()
            );
        });
        
        // Setup network client
        this.setupNetworking();
        
        // Show lobby
        this.showLobby();
        
        // Start game engine
        this.gameEngine.start();
        
        console.log('Bomberman DOM initialized successfully');
    }
    
    /**
     * Setup networking
     */
    setupNetworking() {
        // Connect to server
        this.networkClient.connect();
        
        // Handle connection events
        this.networkClient.on('connected', () => {
            console.log('Connected to game server');
        });
        
        this.networkClient.on('disconnected', () => {
            console.log('Disconnected from game server');
            // Show reconnection message or return to lobby
        });
        
        this.networkClient.on('error', (error) => {
            console.error('Network error:', error);
        });
    }
    
    /**
     * Show lobby screen
     */
    showLobby() {
        this.currentScreen = 'lobby';
        
        // Clear container
        DOM.clear(this.container);
        
        // Create lobby component
        this.lobbyComponent = new LobbyComponent(this.container, {
            onEvent: this.handleLobbyEvent
        });
        
        // Set network client for lobby
        this.lobbyComponent.setNetworkClient(this.networkClient);
        
        // Mount lobby
        this.lobbyComponent.mount();
    }
    
    /**
     * Handle lobby events
     */
    handleLobbyEvent(event, data) {
        switch (event) {
            case 'joinGame':
                console.log('Attempting to join game with nickname:', data.nickname);
                this.networkClient.joinGame(data.nickname);
                break;
                
            case 'gameStart':
                console.log('Game starting with players:', data.players);
                this.startGame(data);
                break;
        }
    }
    
    /**
     * Start the game
     */
    startGame(gameData) {
        this.currentScreen = 'game';
        this.gameStartTime = Date.now();

        // Set game state
        this.gameState.setState('playing');
        this.gameState.playerId = this.networkClient.playerId;

        // Add players to game state
        gameData.players.forEach(playerData => {
            this.gameState.addPlayer(playerData);
        });

        // Generate and display map
        this.mapSystem.generateMap();

        // Clear container and create game board
        DOM.clear(this.container);
        this.createGameBoard();

        // Create UI components
        this.createGameUI();

        console.log('Game started');
    }
    
    /**
     * Create game board
     */
    createGameBoard() {
        // Create game board container
        const gameBoard = DOM.createElement('div', {
            className: 'game-board'
        });

        // Create map
        this.mapSystem.createMapElement(gameBoard);

        // Create player elements
        for (const player of this.gameState.getAllPlayers()) {
            this.playerSystem.createPlayerElement(player, gameBoard);
        }

        this.container.appendChild(gameBoard);
    }

    /**
     * Create game UI components
     */
    createGameUI() {
        // Create HUD
        this.hudComponent = new HUDComponent(this.container);
        this.hudComponent.mount();
        this.hudComponent.updatePlayerInfo(this.gameState.currentPlayer);
        this.hudComponent.updatePlayers(this.gameState.getAllPlayers());

        // Create chat
        this.chatComponent = new ChatComponent(this.container);
        this.chatComponent.setNetworkClient(this.networkClient);
        this.chatComponent.mount();

        // Register for game events
        this.setupGameEventHandlers();
    }

    /**
     * Setup game event handlers
     */
    setupGameEventHandlers() {
        // Player events
        this.gameState.on('playerUpdated', (player) => {
            if (player.id === this.gameState.playerId) {
                this.hudComponent.updatePlayerInfo(player);
            }
            this.hudComponent.updatePlayers(this.gameState.getAllPlayers());
        });

        this.gameState.on('playerKilled', (player) => {
            this.chatComponent.addDeathMessage(player.nickname);
            this.hudComponent.updatePlayers(this.gameState.getAllPlayers());
        });

        // Powerup events
        this.gameState.on('powerupCollected', (data) => {
            this.chatComponent.addSystemMessage(`${data.player.nickname} collected ${data.powerup.type} powerup!`);
        });

        // Game end event
        this.gameState.on('gameEnd', (winner) => {
            if (winner) {
                this.chatComponent.addSystemMessage(`${winner.nickname} wins the game!`);
            } else {
                this.chatComponent.addSystemMessage('Game ended in a draw!');
            }
        });
    }
    
    /**
     * Game update loop
     */
    update(deltaTime) {
        // Update input
        this.inputManager.update();
        
        // Update game systems based on current screen
        if (this.currentScreen === 'game') {
            this.playerSystem.update(deltaTime);
            this.bombSystem.update(deltaTime);
            this.powerupSystem.update(deltaTime);

            // Update HUD with game time
            if (this.hudComponent && this.gameStartTime) {
                const gameTime = Date.now() - this.gameStartTime;
                this.hudComponent.updateGameTime(gameTime);
            }
        }
        
        // Handle debug keys
        if (this.inputManager.isKeyPressed('F1')) {
            this.performanceMonitor.toggle();
        }
        
        if (this.inputManager.isKeyPressed('F5')) {
            location.reload();
        }
    }
    
    /**
     * Game render loop
     */
    render(deltaTime) {
        // Rendering is handled by DOM updates in systems
        // This could be expanded for canvas-based effects
    }
    
    /**
     * Get total entity count for performance monitoring
     */
    getEntityCount() {
        return this.gameState.players.size +
               this.gameState.bombs.size +
               this.gameState.explosions.size +
               this.gameState.powerups.size;
    }
    
    /**
     * Reset game to lobby
     */
    resetGame() {
        console.log('Resetting game...');
        
        // Reset game state
        this.gameState.reset();
        
        // Cleanup systems
        if (this.playerSystem) {
            this.playerSystem.cleanup();
        }
        if (this.mapSystem) {
            this.mapSystem.cleanup();
        }
        if (this.bombSystem) {
            this.bombSystem.cleanup();
        }
        if (this.powerupSystem) {
            this.powerupSystem.cleanup();
        }

        // Cleanup UI components
        if (this.chatComponent) {
            this.chatComponent.destroy();
            this.chatComponent = null;
        }
        if (this.hudComponent) {
            this.hudComponent.destroy();
            this.hudComponent = null;
        }
        
        // Show lobby
        this.showLobby();
    }
    
    /**
     * Cleanup and destroy game
     */
    destroy() {
        console.log('Destroying game...');
        
        // Stop game engine
        this.gameEngine.stop();
        
        // Cleanup systems
        this.inputManager.destroy();
        this.performanceMonitor.destroy();
        this.networkClient.disconnect();
        
        if (this.playerSystem) {
            this.playerSystem.cleanup();
        }
        if (this.mapSystem) {
            this.mapSystem.cleanup();
        }
        if (this.bombSystem) {
            this.bombSystem.cleanup();
        }
        if (this.powerupSystem) {
            this.powerupSystem.cleanup();
        }
        
        // Cleanup UI
        if (this.lobbyComponent) {
            this.lobbyComponent.destroy();
        }
        if (this.chatComponent) {
            this.chatComponent.destroy();
        }
        if (this.hudComponent) {
            this.hudComponent.destroy();
        }
        
        // Clear container
        if (this.container) {
            DOM.clear(this.container);
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, starting Bomberman...');

    try {
        // Create and initialize game
        window.game = new BombermanGame();
        window.game.init();

        console.log('Game initialized successfully');
    } catch (error) {
        console.error('Error initializing game:', error);

        // Show error message to user
        const app = document.getElementById('app');
        if (app) {
            app.innerHTML = `
                <div style="color: white; text-align: center; padding: 50px;">
                    <h1>Error Loading Game</h1>
                    <p>There was an error loading the Bomberman game.</p>
                    <p>Please check the browser console for details.</p>
                    <p>Error: ${error.message}</p>
                </div>
            `;
        }
    }

    // Handle page unload
    window.addEventListener('beforeunload', () => {
        if (window.game) {
            window.game.destroy();
        }
    });
});

// Export for debugging
window.BombermanGame = BombermanGame;
