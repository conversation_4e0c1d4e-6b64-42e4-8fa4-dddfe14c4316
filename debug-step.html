<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM - Step by Step Debug</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ff6600;
            background: rgba(255, 102, 0, 0.1);
        }
        .success {
            border-left-color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        .error {
            border-left-color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <h1>Bomberman DOM - Step by Step Debug</h1>
    <div id="steps"></div>
    
    <script>
        const steps = document.getElementById('steps');
        
        function addStep(message, status = 'info') {
            const div = document.createElement('div');
            div.className = `step ${status}`;
            div.textContent = message;
            steps.appendChild(div);
            console.log(message);
        }
        
        function testStep(description, testFn) {
            try {
                addStep(`Testing: ${description}`, 'info');
                const result = testFn();
                addStep(`✓ ${description} - Success`, 'success');
                return result;
            } catch (error) {
                addStep(`✗ ${description} - Error: ${error.message}`, 'error');
                console.error(`Error in ${description}:`, error);
                throw error;
            }
        }
        
        async function runTests() {
            try {
                // Test 1: Load framework files
                await testStep('Load DOM framework', async () => {
                    await loadScript('framework/dom.js');
                    if (typeof DOM === 'undefined') throw new Error('DOM not defined');
                });
                
                await testStep('Load Component framework', async () => {
                    await loadScript('framework/component.js');
                    if (typeof Component === 'undefined') throw new Error('Component not defined');
                });
                
                await testStep('Load GameEngine', async () => {
                    await loadScript('framework/game-engine.js');
                    if (typeof GameEngine === 'undefined') throw new Error('GameEngine not defined');
                });
                
                // Test 2: Load core systems
                await testStep('Load GameState', async () => {
                    await loadScript('game/core/game-state.js');
                    if (typeof GameState === 'undefined') throw new Error('GameState not defined');
                });
                
                await testStep('Create GameState instance', () => {
                    window.testGameState = new GameState();
                    if (!window.testGameState.on) throw new Error('GameState.on method not found');
                });
                
                await testStep('Load PerformanceMonitor', async () => {
                    await loadScript('game/core/performance.js');
                    if (typeof PerformanceMonitor === 'undefined') throw new Error('PerformanceMonitor not defined');
                });
                
                await testStep('Load InputManager', async () => {
                    await loadScript('game/core/input.js');
                    if (typeof InputManager === 'undefined') throw new Error('InputManager not defined');
                });
                
                // Test 3: Load networking
                await testStep('Load WebSocketClient', async () => {
                    await loadScript('game/network/websocket-client.js');
                    if (typeof WebSocketClient === 'undefined') throw new Error('WebSocketClient not defined');
                });
                
                await testStep('Create WebSocketClient instance', () => {
                    window.testNetworkClient = new WebSocketClient();
                });
                
                // Test 4: Load systems one by one
                await testStep('Load PlayerSystem', async () => {
                    await loadScript('game/systems/player.js');
                    if (typeof PlayerSystem === 'undefined') throw new Error('PlayerSystem not defined');
                });
                
                await testStep('Create PlayerSystem instance', () => {
                    window.testPlayerSystem = new PlayerSystem(window.testGameState, null, window.testNetworkClient);
                });
                
                await testStep('Load MapSystem', async () => {
                    await loadScript('game/systems/map.js');
                    if (typeof MapSystem === 'undefined') throw new Error('MapSystem not defined');
                });
                
                await testStep('Create MapSystem instance', () => {
                    window.testMapSystem = new MapSystem(window.testGameState);
                });
                
                await testStep('Load BombSystem', async () => {
                    await loadScript('game/systems/bomb.js');
                    if (typeof BombSystem === 'undefined') throw new Error('BombSystem not defined');
                });
                
                await testStep('Create BombSystem instance', () => {
                    window.testBombSystem = new BombSystem(window.testGameState, window.testMapSystem, window.testPlayerSystem, window.testNetworkClient);
                });
                
                await testStep('Load PowerupSystem', async () => {
                    await loadScript('game/systems/powerup.js');
                    if (typeof PowerupSystem === 'undefined') throw new Error('PowerupSystem not defined');
                });
                
                await testStep('Create PowerupSystem instance', () => {
                    window.testPowerupSystem = new PowerupSystem(window.testGameState, window.testPlayerSystem);
                });
                
                addStep('🎉 All systems loaded successfully!', 'success');
                
            } catch (error) {
                addStep(`❌ Test failed: ${error.message}`, 'error');
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`Failed to load ${src}`));
                document.head.appendChild(script);
            });
        }
        
        // Start tests
        runTests();
    </script>
</body>
</html>
