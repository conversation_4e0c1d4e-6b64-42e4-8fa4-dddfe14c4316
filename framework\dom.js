/**
 * Mini DOM Framework - Core DOM manipulation utilities
 * Optimized for performance and 60fps gaming
 */

class DOM {
    /**
     * Create a new DOM element with optional attributes and children
     */
    static createElement(tag, attributes = {}, children = []) {
        const element = document.createElement(tag);
        
        // Set attributes
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'style' && typeof value === 'object') {
                Object.assign(element.style, value);
            } else if (key.startsWith('on') && typeof value === 'function') {
                element.addEventListener(key.slice(2).toLowerCase(), value);
            } else {
                element.setAttribute(key, value);
            }
        });
        
        // Add children
        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else if (child instanceof Node) {
                element.appendChild(child);
            }
        });
        
        return element;
    }
    
    /**
     * Query selector with caching for performance
     */
    static query(selector, parent = document) {
        return parent.querySelector(selector);
    }
    
    /**
     * Query all with caching for performance
     */
    static queryAll(selector, parent = document) {
        return Array.from(parent.querySelectorAll(selector));
    }
    
    /**
     * Efficient style updates using transform for positioning
     */
    static setPosition(element, x, y) {
        element.style.transform = `translate(${x}px, ${y}px)`;
    }
    
    /**
     * Batch style updates for performance
     */
    static setStyles(element, styles) {
        Object.assign(element.style, styles);
    }
    
    /**
     * Add class with performance optimization
     */
    static addClass(element, className) {
        if (!element.classList.contains(className)) {
            element.classList.add(className);
        }
    }
    
    /**
     * Remove class with performance optimization
     */
    static removeClass(element, className) {
        if (element.classList.contains(className)) {
            element.classList.remove(className);
        }
    }
    
    /**
     * Toggle class
     */
    static toggleClass(element, className) {
        element.classList.toggle(className);
    }
    
    /**
     * Remove element from DOM
     */
    static remove(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    }
    
    /**
     * Clear all children from element
     */
    static clear(element) {
        while (element.firstChild) {
            element.removeChild(element.firstChild);
        }
    }
    
    /**
     * Get element bounds for collision detection
     */
    static getBounds(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height,
            right: rect.right,
            bottom: rect.bottom
        };
    }
    
    /**
     * Check if point is inside element bounds
     */
    static isPointInside(element, x, y) {
        const bounds = this.getBounds(element);
        return x >= bounds.x && x <= bounds.right && 
               y >= bounds.y && y <= bounds.bottom;
    }
}

// Export for use in other modules
window.DOM = DOM;
console.log('DOM framework loaded');
