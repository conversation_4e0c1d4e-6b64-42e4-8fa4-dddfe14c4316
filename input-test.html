<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .success { color: #66ff66; }
        .error { color: #ff6666; }
        .info { color: #66ccff; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Input and Button Test</h1>
        <div id="app"></div>
        
        <div class="debug">
            <h3>Debug Log</h3>
            <div id="logs"></div>
        </div>
    </div>
    
    <!-- Load all dependencies -->
    <script src="framework/dom.js"></script>
    <script src="framework/component.js"></script>
    <script src="framework/game-engine.js"></script>
    <script src="game/core/game-state.js"></script>
    <script src="game/core/performance.js"></script>
    <script src="game/core/input.js"></script>
    <script src="game/systems/player.js"></script>
    <script src="game/systems/map.js"></script>
    <script src="game/systems/bomb.js"></script>
    <script src="game/systems/powerup.js"></script>
    <script src="game/components/lobby.js"></script>
    <script src="game/components/game-board.js"></script>
    <script src="game/components/chat.js"></script>
    <script src="game/components/hud.js"></script>
    <script src="game/network/websocket-client.js"></script>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        // Test the lobby component directly
        document.addEventListener('DOMContentLoaded', () => {
            try {
                log('Starting input test...', 'info');
                
                const container = document.getElementById('app');
                
                // Create lobby component with test event handler
                const lobby = new LobbyComponent(container, {
                    onEvent: (event, data) => {
                        log(`Event received: ${event}`, 'success');
                        log(`Data: ${JSON.stringify(data)}`, 'info');
                        
                        if (event === 'joinGame') {
                            log(`Join game requested with nickname: "${data.nickname}"`, 'success');
                            
                            // Simulate successful join
                            setTimeout(() => {
                                lobby.handleNetworkMessage('join_success', {
                                    playerCount: 1,
                                    maxPlayers: 4,
                                    playerId: 'test-player-1'
                                });
                                log('Simulated join success', 'success');
                            }, 1000);
                        }
                    }
                });
                
                // Create a mock network client
                const mockNetworkClient = {
                    on: (event, handler) => {
                        log(`Network client listening for: ${event}`, 'info');
                    },
                    joinGame: (nickname) => {
                        log(`Network client joinGame called with: "${nickname}"`, 'success');
                        return true;
                    }
                };
                
                lobby.setNetworkClient(mockNetworkClient);
                lobby.mount();
                
                log('✓ Lobby component created and mounted', 'success');
                
                // Test input functionality
                setTimeout(() => {
                    const input = container.querySelector('input');
                    const button = container.querySelector('button');
                    
                    if (input) {
                        log('✓ Input field found', 'success');
                        
                        // Test typing
                        input.focus();
                        input.value = 'TestPlayer';
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        
                        log(`Input value after test: "${input.value}"`, 'info');
                        
                        setTimeout(() => {
                            if (button && !button.disabled) {
                                log('✓ Button is enabled', 'success');
                                log('Click the button to test join functionality', 'info');
                            } else {
                                log('✗ Button is disabled or not found', 'error');
                            }
                        }, 100);
                        
                    } else {
                        log('✗ Input field not found', 'error');
                    }
                }, 500);
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        });
    </script>
</body>
</html>
