/**
 * Map System
 * Handles map generation, rendering, and block destruction
 */

class MapSystem {
    constructor(gameState) {
        this.gameState = gameState;
        this.mapElement = null;
        this.cellElements = [];
        
        // Map generation settings
        this.blockDensity = 0.7; // Probability of block generation
        this.safeZoneSize = 2; // Safe zone around starting positions
        
        // Starting positions (corners)
        this.startingPositions = [
            { x: 1, y: 1 },     // Top-left
            { x: 13, y: 1 },    // Top-right
            { x: 1, y: 9 },     // Bottom-left
            { x: 13, y: 9 }     // Bottom-right
        ];
        
        // Bind methods
        this.generateMap = this.generateMap.bind(this);
        this.createMapElement = this.createMapElement.bind(this);
        this.destroyBlock = this.destroyBlock.bind(this);
        this.isInSafeZone = this.isInSafeZone.bind(this);
    }
    
    /**
     * Initialize map system
     */
    init() {
        console.log('Map system initialized');
    }
    
    /**
     * Generate a new map
     */
    generateMap() {
        const width = this.gameState.settings.mapWidth;
        const height = this.gameState.settings.mapHeight;
        
        // Initialize empty map
        const map = Array(height).fill().map(() => Array(width).fill(0));
        
        // Place walls (fixed pattern)
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                // Border walls
                if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
                    map[y][x] = 1; // Wall
                }
                // Internal walls (every other cell starting from 2,2)
                else if (x % 2 === 0 && y % 2 === 0) {
                    map[y][x] = 1; // Wall
                }
            }
        }
        
        // Place random blocks
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                // Skip if already a wall
                if (map[y][x] === 1) continue;
                
                // Skip safe zones around starting positions
                if (this.isInSafeZone(x, y)) continue;
                
                // Randomly place blocks
                if (Math.random() < this.blockDensity) {
                    map[y][x] = 2; // Block
                }
            }
        }
        
        // Store map in game state
        this.gameState.setMap(map);
        
        console.log('Map generated');
        return map;
    }
    
    /**
     * Check if position is in safe zone around starting positions
     */
    isInSafeZone(x, y) {
        for (const pos of this.startingPositions) {
            const distance = Math.abs(x - pos.x) + Math.abs(y - pos.y);
            if (distance <= this.safeZoneSize) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Create map DOM element
     */
    createMapElement(container) {
        const width = this.gameState.settings.mapWidth;
        const height = this.gameState.settings.mapHeight;
        const cellSize = this.gameState.settings.cellSize;
        
        // Create map container
        this.mapElement = DOM.createElement('div', {
            className: 'game-grid',
            style: {
                position: 'relative',
                width: (width * cellSize) + 'px',
                height: (height * cellSize) + 'px',
                display: 'grid',
                gridTemplateColumns: `repeat(${width}, ${cellSize}px)`,
                gridTemplateRows: `repeat(${height}, ${cellSize}px)`
            }
        });
        
        // Create cell elements
        this.cellElements = [];
        for (let y = 0; y < height; y++) {
            this.cellElements[y] = [];
            for (let x = 0; x < width; x++) {
                const cell = this.gameState.getCell(x, y);
                const cellElement = this.createCellElement(x, y, cell);
                this.cellElements[y][x] = cellElement;
                this.mapElement.appendChild(cellElement);
            }
        }
        
        container.appendChild(this.mapElement);
        return this.mapElement;
    }
    
    /**
     * Create individual cell element
     */
    createCellElement(x, y, cellType) {
        let className = 'cell';
        let content = '';
        
        switch (cellType) {
            case 0: // Empty
                className += ' empty';
                break;
            case 1: // Wall
                className += ' wall';
                break;
            case 2: // Block
                className += ' block';
                break;
        }
        
        const element = DOM.createElement('div', {
            className: className,
            'data-x': x,
            'data-y': y,
            style: {
                width: this.gameState.settings.cellSize + 'px',
                height: this.gameState.settings.cellSize + 'px',
                position: 'relative'
            }
        }, [content]);
        
        return element;
    }
    
    /**
     * Update cell visual
     */
    updateCell(x, y, newType) {
        if (!this.cellElements || !this.cellElements[y] || !this.cellElements[y][x]) {
            return;
        }
        
        const element = this.cellElements[y][x];
        
        // Remove old classes
        DOM.removeClass(element, 'empty');
        DOM.removeClass(element, 'wall');
        DOM.removeClass(element, 'block');
        
        // Add new class
        switch (newType) {
            case 0: // Empty
                DOM.addClass(element, 'empty');
                break;
            case 1: // Wall
                DOM.addClass(element, 'wall');
                break;
            case 2: // Block
                DOM.addClass(element, 'block');
                break;
        }
        
        // Update game state
        this.gameState.setCell(x, y, newType);
    }
    
    /**
     * Destroy block at position
     */
    destroyBlock(x, y) {
        const cell = this.gameState.getCell(x, y);
        
        // Only destroy blocks, not walls
        if (cell !== 2) {
            return false;
        }
        
        // Update to empty cell
        this.updateCell(x, y, 0);
        
        // Chance to spawn powerup
        if (Math.random() < 0.3) { // 30% chance
            this.spawnPowerup(x, y);
        }
        
        console.log(`Block destroyed at (${x}, ${y})`);
        return true;
    }
    
    /**
     * Spawn powerup at position
     */
    spawnPowerup(x, y) {
        const powerupTypes = ['bomb', 'flame', 'speed'];
        const type = powerupTypes[Math.floor(Math.random() * powerupTypes.length)];
        
        const powerup = {
            type: type,
            x: x * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2,
            y: y * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2
        };
        
        this.gameState.addPowerup(powerup);
        console.log(`Powerup ${type} spawned at (${x}, ${y})`);
    }
    
    /**
     * Get cell type at pixel coordinates
     */
    getCellAtPixel(pixelX, pixelY) {
        const gridPos = this.gameState.pixelToGrid(pixelX, pixelY);
        return this.gameState.getCell(gridPos.x, gridPos.y);
    }
    
    /**
     * Check if position is destructible
     */
    isDestructible(x, y) {
        const cell = this.gameState.getCell(x, y);
        return cell === 2; // Only blocks are destructible
    }
    
    /**
     * Get all destructible blocks
     */
    getDestructibleBlocks() {
        const blocks = [];
        const width = this.gameState.settings.mapWidth;
        const height = this.gameState.settings.mapHeight;
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                if (this.isDestructible(x, y)) {
                    blocks.push({ x, y });
                }
            }
        }
        
        return blocks;
    }
    
    /**
     * Get starting position for player
     */
    getStartingPosition(playerIndex) {
        if (playerIndex >= 0 && playerIndex < this.startingPositions.length) {
            const gridPos = this.startingPositions[playerIndex];
            return this.gameState.gridToPixel(gridPos.x, gridPos.y);
        }
        return { x: 0, y: 0 };
    }
    
    /**
     * Check line of sight between two points (for AI)
     */
    hasLineOfSight(x1, y1, x2, y2) {
        // Simple line of sight check using Bresenham's line algorithm
        const dx = Math.abs(x2 - x1);
        const dy = Math.abs(y2 - y1);
        const sx = x1 < x2 ? 1 : -1;
        const sy = y1 < y2 ? 1 : -1;
        let err = dx - dy;
        
        let x = x1;
        let y = y1;
        
        while (true) {
            // Check if current cell blocks line of sight
            const cell = this.gameState.getCell(x, y);
            if (cell === 1 || cell === 2) { // Wall or block
                return false;
            }
            
            // Reached target
            if (x === x2 && y === y2) {
                return true;
            }
            
            const e2 = 2 * err;
            if (e2 > -dy) {
                err -= dy;
                x += sx;
            }
            if (e2 < dx) {
                err += dx;
                y += sy;
            }
        }
    }
    
    /**
     * Get safe positions (for AI pathfinding)
     */
    getSafePositions() {
        const safe = [];
        const width = this.gameState.settings.mapWidth;
        const height = this.gameState.settings.mapHeight;
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                if (this.gameState.isWalkable(x, y)) {
                    safe.push({ x, y });
                }
            }
        }
        
        return safe;
    }
    
    /**
     * Cleanup map
     */
    cleanup() {
        if (this.mapElement) {
            DOM.remove(this.mapElement);
            this.mapElement = null;
        }
        this.cellElements = [];
    }
}

// Export for use in other modules
window.MapSystem = MapSystem;
