<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Main Game Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .error { color: #ff6666; }
        .success { color: #66ff66; }
        .warning { color: #ffff66; }
    </style>
</head>
<body>
    <div id="app">
        <!-- Game will be rendered here -->
    </div>
    
    <div class="debug">
        <h3>Main Game Test - Load Main Game File</h3>
        <div id="logs"></div>
        <button onclick="testMainGame()" style="margin-top: 10px;">Test Main Game</button>
        <button onclick="testGameCreation()" style="margin-top: 10px;">Test Game Creation</button>
    </div>
    
    <!-- Load all dependencies first -->
    <!-- Mini Framework -->
    <script src="framework/dom.js"></script>
    <script src="framework/component.js"></script>
    <script src="framework/game-engine.js"></script>
    
    <!-- Game Core -->
    <script src="game/core/game-state.js"></script>
    <script src="game/core/performance.js"></script>
    <script src="game/core/input.js"></script>

    <!-- Game Systems -->
    <script src="game/systems/player.js"></script>
    <script src="game/systems/map.js"></script>
    <script src="game/systems/bomb.js"></script>
    <script src="game/systems/powerup.js"></script>

    <!-- Game Components -->
    <script src="game/components/lobby.js"></script>
    <script src="game/components/game-board.js"></script>
    <script src="game/components/chat.js"></script>
    <script src="game/components/hud.js"></script>

    <!-- Networking -->
    <script src="game/network/websocket-client.js"></script>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Catch unhandled errors
        window.addEventListener('error', (event) => {
            log(`UNHANDLED ERROR: ${event.error.message}`, 'error');
            log(`File: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            log(`Stack: ${event.error.stack}`, 'error');
        });
        
        log('All dependencies loaded, ready for main game test');
        
        async function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`Failed to load ${src}`));
                document.head.appendChild(script);
            });
        }
        
        async function testMainGame() {
            try {
                log('Testing main game file load...');
                
                // Load the main game file (but it will auto-initialize)
                await loadScript('game/main.js');
                
                log('✓ Main game file loaded', 'success');
                
                if (typeof BombermanGame !== 'undefined') {
                    log('✓ BombermanGame class is available', 'success');
                } else {
                    log('✗ BombermanGame class not found', 'error');
                }
                
            } catch (error) {
                log(`❌ Main game test failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        function testGameCreation() {
            try {
                log('Testing BombermanGame creation manually...');
                
                if (typeof BombermanGame === 'undefined') {
                    log('✗ BombermanGame class not available', 'error');
                    return;
                }
                
                log('Creating BombermanGame instance...');
                const game = new BombermanGame();
                log('✓ BombermanGame instance created successfully', 'success');
                
                log('Testing game initialization...');
                game.init();
                log('✓ Game initialized successfully', 'success');
                
            } catch (error) {
                log(`❌ Game creation failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
    </script>
</body>
</html>
