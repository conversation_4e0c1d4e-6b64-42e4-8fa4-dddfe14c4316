<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM - Minimal Test</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .lobby {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            min-width: 400px;
        }
        .lobby h1 {
            color: #ff6600;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .lobby input {
            padding: 10px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            margin: 10px;
            width: 200px;
        }
        .lobby button {
            padding: 10px 20px;
            font-size: 16px;
            background: #ff6600;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Game will be rendered here -->
    </div>
    
    <script>
        console.log('Minimal test starting...');
        
        // Test basic DOM manipulation
        const app = document.getElementById('app');
        
        // Create a simple lobby manually
        const lobby = document.createElement('div');
        lobby.className = 'lobby';
        lobby.innerHTML = `
            <h1>Bomberman DOM</h1>
            <p>Minimal Test Version</p>
            <input type="text" placeholder="Your nickname" id="nickname">
            <br>
            <button onclick="testJoin()">Join Game</button>
            <div id="status"></div>
        `;
        
        app.appendChild(lobby);
        
        function testJoin() {
            const nickname = document.getElementById('nickname').value;
            const status = document.getElementById('status');
            
            if (nickname.trim()) {
                status.innerHTML = `<p>Attempting to connect as ${nickname}...</p>`;
                
                // Test WebSocket connection
                try {
                    const ws = new WebSocket('ws://localhost:8080/ws');
                    
                    ws.onopen = () => {
                        console.log('WebSocket connected');
                        status.innerHTML = `<p style="color: #00ff00;">Connected! Joining as ${nickname}...</p>`;
                        
                        // Send join message
                        ws.send(JSON.stringify({
                            type: 'join',
                            data: { nickname: nickname },
                            timestamp: Date.now()
                        }));
                    };
                    
                    ws.onmessage = (event) => {
                        console.log('Received message:', event.data);
                        const message = JSON.parse(event.data);
                        
                        if (message.type === 'join_success') {
                            status.innerHTML = `<p style="color: #00ff00;">Successfully joined! Player ID: ${message.data.playerId}</p>`;
                        } else if (message.type === 'join_error') {
                            status.innerHTML = `<p style="color: #ff0000;">Join failed: ${message.data.message}</p>`;
                        }
                    };
                    
                    ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        status.innerHTML = `<p style="color: #ff0000;">Connection failed!</p>`;
                    };
                    
                    ws.onclose = () => {
                        console.log('WebSocket closed');
                        status.innerHTML = `<p style="color: #ffff00;">Connection closed</p>`;
                    };
                    
                } catch (error) {
                    console.error('WebSocket creation failed:', error);
                    status.innerHTML = `<p style="color: #ff0000;">WebSocket not supported!</p>`;
                }
            } else {
                status.innerHTML = `<p style="color: #ff0000;">Please enter a nickname!</p>`;
            }
        }
        
        // Make function global
        window.testJoin = testJoin;
        
        console.log('Minimal test ready');
    </script>
</body>
</html>
