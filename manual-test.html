<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
        }
        .debug .log {
            margin: 2px 0;
            padding: 2px;
        }
        .debug .error {
            color: #ff6666;
        }
        .debug .success {
            color: #66ff66;
        }
        .debug .warning {
            color: #ffff66;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Game will be rendered here -->
    </div>
    
    <div class="debug" id="debug">
        <div><strong>Debug Log</strong></div>
        <div id="logs"></div>
        <button onclick="startTest()" style="margin-top: 10px;">Start Manual Test</button>
    </div>
    
    <!-- Mini Framework -->
    <script src="framework/dom.js"></script>
    <script src="framework/component.js"></script>
    <script src="framework/game-engine.js"></script>
    
    <!-- Game Core -->
    <script src="game/core/game-state.js"></script>
    <script src="game/core/performance.js"></script>
    <script src="game/core/input.js"></script>

    <!-- Game Systems -->
    <script src="game/systems/player.js"></script>
    <script src="game/systems/map.js"></script>
    <script src="game/systems/bomb.js"></script>
    <script src="game/systems/powerup.js"></script>

    <!-- Game Components -->
    <script src="game/components/lobby.js"></script>
    <script src="game/components/game-board.js"></script>
    <script src="game/components/chat.js"></script>
    <script src="game/components/hud.js"></script>

    <!-- Networking -->
    <script src="game/network/websocket-client.js"></script>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Catch unhandled errors
        window.addEventListener('error', (event) => {
            log(`UNHANDLED ERROR: ${event.error.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        log('All scripts loaded, ready for manual test');
        
        function startTest() {
            try {
                log('Starting manual test...');
                
                // Test 1: Check if all classes are available
                const classes = [
                    'DOM', 'Component', 'GameEngine', 'GameState', 
                    'PerformanceMonitor', 'InputManager', 'PlayerSystem', 
                    'MapSystem', 'BombSystem', 'PowerupSystem', 
                    'LobbyComponent', 'WebSocketClient'
                ];
                
                for (const className of classes) {
                    if (typeof window[className] !== 'undefined') {
                        log(`✓ ${className} available`, 'success');
                    } else {
                        log(`✗ ${className} not found`, 'error');
                        return;
                    }
                }
                
                // Test 2: Create instances step by step
                log('Creating GameState...');
                const gameState = new GameState();
                log('✓ GameState created', 'success');
                
                log('Creating PerformanceMonitor...');
                const performanceMonitor = new PerformanceMonitor();
                log('✓ PerformanceMonitor created', 'success');
                
                log('Creating InputManager...');
                const inputManager = new InputManager();
                log('✓ InputManager created', 'success');
                
                log('Creating WebSocketClient...');
                const networkClient = new WebSocketClient();
                log('✓ WebSocketClient created', 'success');
                
                log('Creating PlayerSystem...');
                const playerSystem = new PlayerSystem(gameState, inputManager, networkClient);
                log('✓ PlayerSystem created', 'success');
                
                log('Creating MapSystem...');
                const mapSystem = new MapSystem(gameState);
                log('✓ MapSystem created', 'success');
                
                log('Creating BombSystem...');
                const bombSystem = new BombSystem(gameState, mapSystem, playerSystem, networkClient);
                log('✓ BombSystem created', 'success');
                
                log('Creating PowerupSystem...');
                const powerupSystem = new PowerupSystem(gameState, playerSystem);
                log('✓ PowerupSystem created', 'success');
                
                log('Creating LobbyComponent...');
                const container = document.getElementById('app');
                const lobbyComponent = new LobbyComponent(container);
                log('✓ LobbyComponent created', 'success');
                
                log('🎉 All systems created successfully!', 'success');
                
                // Test 3: Try to initialize systems
                log('Initializing systems...');
                
                performanceMonitor.init();
                log('✓ PerformanceMonitor initialized', 'success');
                
                inputManager.init();
                log('✓ InputManager initialized', 'success');
                
                playerSystem.init();
                log('✓ PlayerSystem initialized', 'success');
                
                mapSystem.init();
                log('✓ MapSystem initialized', 'success');
                
                bombSystem.init();
                log('✓ BombSystem initialized', 'success');
                
                powerupSystem.init();
                log('✓ PowerupSystem initialized', 'success');
                
                log('🎉 All systems initialized successfully!', 'success');
                
                // Test 4: Try to render lobby
                log('Rendering lobby...');
                lobbyComponent.render();
                log('✓ Lobby rendered successfully!', 'success');
                
            } catch (error) {
                log(`Test failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
