/**
 * Performance Monitor
 * Tracks and displays performance metrics for 60fps gaming
 */

class PerformanceMonitor {
    constructor() {
        this.isEnabled = true;
        this.displayElement = null;
        
        // Performance metrics
        this.metrics = {
            fps: 0,
            frameTime: 0,
            updateTime: 0,
            renderTime: 0,
            memoryUsage: 0,
            entityCount: 0
        };
        
        // FPS calculation
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.frameTimes = [];
        this.maxFrameTimeHistory = 60;
        
        // Performance thresholds
        this.thresholds = {
            targetFPS: 60,
            warningFPS: 50,
            criticalFPS: 30,
            maxFrameTime: 16.67, // 60fps = 16.67ms per frame
            warningFrameTime: 20,
            criticalFrameTime: 33.33
        };
        
        // Warnings
        this.warnings = {
            lowFPS: false,
            highFrameTime: false,
            memoryLeak: false
        };
        
        this.lastMemoryCheck = 0;
        this.memoryCheckInterval = 5000; // Check memory every 5 seconds
        
        // Bind methods
        this.update = this.update.bind(this);
        this.render = this.render.bind(this);
        this.createDisplay = this.createDisplay.bind(this);
        this.updateDisplay = this.updateDisplay.bind(this);
    }
    
    /**
     * Initialize performance monitor
     */
    init() {
        if (this.isEnabled) {
            this.createDisplay();
        }
    }
    
    /**
     * Update performance metrics
     */
    update(deltaTime, updateTime, renderTime, entityCount = 0) {
        const currentTime = performance.now();
        
        // Update frame time
        this.metrics.frameTime = deltaTime;
        this.metrics.updateTime = updateTime;
        this.metrics.renderTime = renderTime;
        this.metrics.entityCount = entityCount;
        
        // Track frame times for analysis
        this.frameTimes.push(deltaTime);
        if (this.frameTimes.length > this.maxFrameTimeHistory) {
            this.frameTimes.shift();
        }
        
        // Calculate FPS
        this.frameCount++;
        if (currentTime - this.lastFPSUpdate >= 1000) {
            this.metrics.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFPSUpdate = currentTime;
            
            // Check for performance issues
            this.checkPerformance();
        }
        
        // Check memory usage periodically
        if (currentTime - this.lastMemoryCheck >= this.memoryCheckInterval) {
            this.checkMemoryUsage();
            this.lastMemoryCheck = currentTime;
        }
        
        // Update display
        if (this.isEnabled && this.displayElement) {
            this.updateDisplay();
        }
    }
    
    /**
     * Check for performance issues
     */
    checkPerformance() {
        const fps = this.metrics.fps;
        const avgFrameTime = this.getAverageFrameTime();
        
        // Check FPS
        if (fps < this.thresholds.criticalFPS) {
            if (!this.warnings.lowFPS) {
                console.error(`Critical FPS: ${fps} (target: ${this.thresholds.targetFPS})`);
                this.warnings.lowFPS = true;
            }
        } else if (fps < this.thresholds.warningFPS) {
            if (!this.warnings.lowFPS) {
                console.warn(`Low FPS: ${fps} (target: ${this.thresholds.targetFPS})`);
                this.warnings.lowFPS = true;
            }
        } else {
            this.warnings.lowFPS = false;
        }
        
        // Check frame time
        if (avgFrameTime > this.thresholds.criticalFrameTime) {
            if (!this.warnings.highFrameTime) {
                console.error(`Critical frame time: ${avgFrameTime.toFixed(2)}ms (target: ${this.thresholds.maxFrameTime.toFixed(2)}ms)`);
                this.warnings.highFrameTime = true;
            }
        } else if (avgFrameTime > this.thresholds.warningFrameTime) {
            if (!this.warnings.highFrameTime) {
                console.warn(`High frame time: ${avgFrameTime.toFixed(2)}ms (target: ${this.thresholds.maxFrameTime.toFixed(2)}ms)`);
                this.warnings.highFrameTime = true;
            }
        } else {
            this.warnings.highFrameTime = false;
        }
    }
    
    /**
     * Check memory usage
     */
    checkMemoryUsage() {
        if (performance.memory) {
            const memory = performance.memory;
            this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
            
            // Check for potential memory leaks
            const memoryGrowthRate = this.metrics.memoryUsage / (memory.totalJSHeapSize / 1024 / 1024);
            if (memoryGrowthRate > 0.8) {
                if (!this.warnings.memoryLeak) {
                    console.warn(`High memory usage: ${this.metrics.memoryUsage.toFixed(2)}MB`);
                    this.warnings.memoryLeak = true;
                }
            } else {
                this.warnings.memoryLeak = false;
            }
        }
    }
    
    /**
     * Get average frame time
     */
    getAverageFrameTime() {
        if (this.frameTimes.length === 0) return 0;
        const sum = this.frameTimes.reduce((a, b) => a + b, 0);
        return sum / this.frameTimes.length;
    }
    
    /**
     * Get performance grade
     */
    getPerformanceGrade() {
        const fps = this.metrics.fps;
        const frameTime = this.getAverageFrameTime();
        
        if (fps >= this.thresholds.targetFPS && frameTime <= this.thresholds.maxFrameTime) {
            return 'A'; // Excellent
        } else if (fps >= this.thresholds.warningFPS && frameTime <= this.thresholds.warningFrameTime) {
            return 'B'; // Good
        } else if (fps >= this.thresholds.criticalFPS && frameTime <= this.thresholds.criticalFrameTime) {
            return 'C'; // Acceptable
        } else {
            return 'D'; // Poor
        }
    }
    
    /**
     * Create performance display element
     */
    createDisplay() {
        this.displayElement = DOM.createElement('div', {
            className: 'performance',
            style: {
                position: 'fixed',
                top: '10px',
                right: '10px',
                background: 'rgba(0, 0, 0, 0.8)',
                color: '#ffffff',
                padding: '10px',
                borderRadius: '5px',
                fontFamily: 'monospace',
                fontSize: '12px',
                zIndex: '1000',
                minWidth: '200px'
            }
        });
        
        document.body.appendChild(this.displayElement);
    }
    
    /**
     * Update performance display
     */
    updateDisplay() {
        if (!this.displayElement) return;
        
        const grade = this.getPerformanceGrade();
        const gradeColor = {
            'A': '#00ff00',
            'B': '#ffff00',
            'C': '#ff8800',
            'D': '#ff0000'
        }[grade];
        
        const avgFrameTime = this.getAverageFrameTime();
        
        this.displayElement.innerHTML = `
            <div style="color: ${gradeColor}; font-weight: bold; margin-bottom: 5px;">
                Performance: ${grade}
            </div>
            <div>FPS: ${this.metrics.fps}</div>
            <div>Frame: ${avgFrameTime.toFixed(2)}ms</div>
            <div>Update: ${this.metrics.updateTime.toFixed(2)}ms</div>
            <div>Render: ${this.metrics.renderTime.toFixed(2)}ms</div>
            <div>Entities: ${this.metrics.entityCount}</div>
            ${this.metrics.memoryUsage > 0 ? `<div>Memory: ${this.metrics.memoryUsage.toFixed(2)}MB</div>` : ''}
            ${this.hasWarnings() ? `<div style="color: #ff4444; margin-top: 5px;">⚠ Issues detected</div>` : ''}
        `;
    }
    
    /**
     * Check if there are any performance warnings
     */
    hasWarnings() {
        return Object.values(this.warnings).some(warning => warning);
    }
    
    /**
     * Get detailed performance report
     */
    getReport() {
        return {
            metrics: { ...this.metrics },
            averageFrameTime: this.getAverageFrameTime(),
            grade: this.getPerformanceGrade(),
            warnings: { ...this.warnings },
            frameTimes: [...this.frameTimes]
        };
    }
    
    /**
     * Enable/disable performance monitoring
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (enabled && !this.displayElement) {
            this.createDisplay();
        } else if (!enabled && this.displayElement) {
            this.displayElement.remove();
            this.displayElement = null;
        }
    }
    
    /**
     * Toggle performance display
     */
    toggle() {
        this.setEnabled(!this.isEnabled);
    }
    
    /**
     * Reset performance metrics
     */
    reset() {
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.frameTimes = [];
        this.warnings = {
            lowFPS: false,
            highFrameTime: false,
            memoryLeak: false
        };
        
        this.metrics = {
            fps: 0,
            frameTime: 0,
            updateTime: 0,
            renderTime: 0,
            memoryUsage: 0,
            entityCount: 0
        };
    }
    
    /**
     * Cleanup
     */
    destroy() {
        if (this.displayElement) {
            this.displayElement.remove();
            this.displayElement = null;
        }
    }
}

// Export for use in other modules
window.PerformanceMonitor = PerformanceMonitor;
