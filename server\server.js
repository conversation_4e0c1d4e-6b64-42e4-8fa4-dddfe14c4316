/**
 * Bomberman DOM - WebSocket Server
 * Handles multiplayer connections, game state synchronization, and chat
 */

const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');

class BombermanServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.wss = new WebSocket.Server({ server: this.server });
        
        // Game state
        this.gameState = {
            status: 'waiting', // waiting, countdown, playing, finished
            players: new Map(),
            maxPlayers: 4,
            minPlayers: 2,
            waitTime: 20000, // 20 seconds
            countdownTime: 10000, // 10 seconds
            gameStartTime: null,
            countdownStartTime: null
        };
        
        // Timers
        this.waitTimer = null;
        this.countdownTimer = null;
        
        this.setupExpress();
        this.setupWebSocket();
        this.setupGameLogic();
    }
    
    setupExpress() {
        // Serve static files
        this.app.use(express.static(path.join(__dirname, '..')));
        
        // Main route
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '..', 'index.html'));
        });
        
        console.log('Express server configured');
    }
    
    setupWebSocket() {
        this.wss.on('connection', (ws) => {
            console.log('New WebSocket connection');
            
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.handleMessage(ws, message);
                } catch (error) {
                    console.error('Invalid message format:', error);
                }
            });
            
            ws.on('close', () => {
                this.handleDisconnection(ws);
            });
            
            ws.on('error', (error) => {
                console.error('WebSocket error:', error);
            });
        });
        
        console.log('WebSocket server configured');
    }
    
    setupGameLogic() {
        // Game loop for server-side updates
        setInterval(() => {
            this.updateGameState();
        }, 1000 / 60); // 60 FPS server updates
        
        console.log('Game logic configured');
    }
    
    handleMessage(ws, message) {
        switch (message.type) {
            case 'join':
                this.handlePlayerJoin(ws, message.data);
                break;
            case 'chat':
                this.handleChatMessage(ws, message.data);
                break;
            case 'player_move':
                this.handlePlayerMove(ws, message.data);
                break;
            case 'place_bomb':
                this.handlePlaceBomb(ws, message.data);
                break;
            case 'ping':
                this.sendToClient(ws, { type: 'pong', timestamp: Date.now() });
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    handlePlayerJoin(ws, data) {
        const { nickname } = data;
        
        // Check if game is full
        if (this.gameState.players.size >= this.gameState.maxPlayers) {
            this.sendToClient(ws, {
                type: 'join_error',
                message: 'Game is full'
            });
            return;
        }
        
        // Check if game is already in progress
        if (this.gameState.status === 'playing') {
            this.sendToClient(ws, {
                type: 'join_error',
                message: 'Game is already in progress'
            });
            return;
        }
        
        // Create player
        const playerId = this.generatePlayerId();
        const player = {
            id: playerId,
            nickname: nickname,
            ws: ws,
            x: 0,
            y: 0,
            lives: 3,
            powerups: {
                bombs: 1,
                flames: 1,
                speed: 1
            },
            isAlive: true
        };
        
        // Add player to game
        this.gameState.players.set(playerId, player);
        ws.playerId = playerId;
        
        console.log(`Player ${nickname} joined (${playerId})`);
        
        // Send join confirmation
        this.sendToClient(ws, {
            type: 'join_success',
            data: {
                playerId: playerId,
                playerCount: this.gameState.players.size,
                maxPlayers: this.gameState.maxPlayers
            }
        });
        
        // Broadcast player count update
        this.broadcastPlayerCount();
        
        // Check if we should start countdown
        this.checkGameStart();
    }
    
    handleChatMessage(ws, data) {
        const player = this.getPlayerByWs(ws);
        if (!player) return;
        
        const chatMessage = {
            type: 'chat_message',
            data: {
                playerId: player.id,
                nickname: player.nickname,
                message: data.message,
                timestamp: Date.now()
            }
        };
        
        // Broadcast to all players
        this.broadcast(chatMessage);
    }
    
    handlePlayerMove(ws, data) {
        const player = this.getPlayerByWs(ws);
        if (!player || !player.isAlive) return;
        
        // Update player position
        player.x = data.x;
        player.y = data.y;
        
        // Broadcast position update
        this.broadcast({
            type: 'player_moved',
            data: {
                playerId: player.id,
                x: player.x,
                y: player.y
            }
        });
    }
    
    handlePlaceBomb(ws, data) {
        const player = this.getPlayerByWs(ws);
        if (!player || !player.isAlive) return;
        
        // Broadcast bomb placement
        this.broadcast({
            type: 'bomb_placed',
            data: {
                playerId: player.id,
                x: data.x,
                y: data.y,
                power: player.powerups.flames
            }
        });
    }
    
    handleDisconnection(ws) {
        const player = this.getPlayerByWs(ws);
        if (player) {
            console.log(`Player ${player.nickname} disconnected`);
            this.gameState.players.delete(player.id);
            
            // Broadcast player count update
            this.broadcastPlayerCount();
            
            // Reset game if not enough players
            if (this.gameState.players.size < this.gameState.minPlayers) {
                this.resetGame();
            }
        }
    }
    
    checkGameStart() {
        const playerCount = this.gameState.players.size;
        
        if (playerCount >= this.gameState.maxPlayers) {
            // Start countdown immediately if full
            this.startCountdown();
        } else if (playerCount >= this.gameState.minPlayers && !this.waitTimer) {
            // Start wait timer if minimum players reached
            this.startWaitTimer();
        }
    }
    
    startWaitTimer() {
        console.log('Starting wait timer...');
        this.waitTimer = setTimeout(() => {
            if (this.gameState.players.size >= this.gameState.minPlayers) {
                this.startCountdown();
            }
        }, this.gameState.waitTime);
    }
    
    startCountdown() {
        console.log('Starting countdown...');
        this.gameState.status = 'countdown';
        this.gameState.countdownStartTime = Date.now();
        
        // Clear wait timer if it exists
        if (this.waitTimer) {
            clearTimeout(this.waitTimer);
            this.waitTimer = null;
        }
        
        // Broadcast countdown start
        this.broadcast({
            type: 'countdown_start',
            data: {
                duration: this.gameState.countdownTime
            }
        });
        
        // Start game after countdown
        this.countdownTimer = setTimeout(() => {
            this.startGame();
        }, this.gameState.countdownTime);
    }
    
    startGame() {
        console.log('Starting game...');
        this.gameState.status = 'playing';
        this.gameState.gameStartTime = Date.now();
        
        // Assign starting positions
        this.assignStartingPositions();
        
        // Broadcast game start
        this.broadcast({
            type: 'game_start',
            data: {
                players: Array.from(this.gameState.players.values()).map(p => ({
                    id: p.id,
                    nickname: p.nickname,
                    x: p.x,
                    y: p.y,
                    lives: p.lives,
                    powerups: p.powerups
                }))
            }
        });
    }
    
    assignStartingPositions() {
        const positions = [
            { x: 1, y: 1 },     // Top-left
            { x: 13, y: 1 },    // Top-right
            { x: 1, y: 9 },     // Bottom-left
            { x: 13, y: 9 }     // Bottom-right
        ];
        
        let index = 0;
        for (const player of this.gameState.players.values()) {
            const pos = positions[index % positions.length];
            player.x = pos.x;
            player.y = pos.y;
            index++;
        }
    }
    
    resetGame() {
        console.log('Resetting game...');
        this.gameState.status = 'waiting';
        this.gameState.gameStartTime = null;
        this.gameState.countdownStartTime = null;
        
        // Clear timers
        if (this.waitTimer) {
            clearTimeout(this.waitTimer);
            this.waitTimer = null;
        }
        if (this.countdownTimer) {
            clearTimeout(this.countdownTimer);
            this.countdownTimer = null;
        }
        
        // Broadcast reset
        this.broadcast({
            type: 'game_reset'
        });
    }
    
    updateGameState() {
        // Server-side game state updates
        // This will be expanded for bomb explosions, collision detection, etc.
    }
    
    broadcastPlayerCount() {
        this.broadcast({
            type: 'player_count_update',
            data: {
                playerCount: this.gameState.players.size,
                maxPlayers: this.gameState.maxPlayers
            }
        });
    }
    
    broadcast(message) {
        const messageStr = JSON.stringify(message);
        this.gameState.players.forEach(player => {
            if (player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(messageStr);
            }
        });
    }
    
    sendToClient(ws, message) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }
    
    getPlayerByWs(ws) {
        return this.gameState.players.get(ws.playerId);
    }
    
    generatePlayerId() {
        return 'player_' + Math.random().toString(36).substr(2, 9);
    }
    
    start(port = 3000) {
        this.server.listen(port, () => {
            console.log(`Bomberman server running on port ${port}`);
            console.log(`Open http://localhost:${port} to play`);
        });
    }
}

// Start the server
const server = new BombermanServer();
server.start(process.env.PORT || 8080);
