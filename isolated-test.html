<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Isolated Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .error { color: #ff6666; }
        .success { color: #66ff66; }
        .warning { color: #ffff66; }
    </style>
</head>
<body>
    <div id="app">
        <!-- Game will be rendered here -->
    </div>
    
    <div class="debug">
        <h3>Isolated Test - Load Each System Individually</h3>
        <div id="logs"></div>
        <button onclick="runTest()" style="margin-top: 10px;">Run Test</button>
    </div>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Catch unhandled errors
        window.addEventListener('error', (event) => {
            log(`UNHANDLED ERROR: ${event.error.message} at ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            log(`Stack: ${event.error.stack}`, 'error');
        });
        
        async function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`Failed to load ${src}`));
                document.head.appendChild(script);
            });
        }
        
        async function runTest() {
            try {
                log('Starting isolated test...');
                
                // Load framework files
                log('Loading DOM framework...');
                await loadScript('framework/dom.js');
                log('✓ DOM framework loaded', 'success');
                
                log('Loading Component framework...');
                await loadScript('framework/component.js');
                log('✓ Component framework loaded', 'success');
                
                log('Loading GameEngine...');
                await loadScript('framework/game-engine.js');
                log('✓ GameEngine loaded', 'success');
                
                // Load core systems
                log('Loading GameState...');
                await loadScript('game/core/game-state.js');
                log('✓ GameState loaded', 'success');
                
                log('Loading PerformanceMonitor...');
                await loadScript('game/core/performance.js');
                log('✓ PerformanceMonitor loaded', 'success');
                
                log('Loading InputManager...');
                await loadScript('game/core/input.js');
                log('✓ InputManager loaded', 'success');
                
                // Load networking
                log('Loading WebSocketClient...');
                await loadScript('game/network/websocket-client.js');
                log('✓ WebSocketClient loaded', 'success');
                
                // Test creating instances one by one
                log('Creating GameState instance...');
                const gameState = new GameState();
                log('✓ GameState instance created', 'success');
                
                log('Creating PerformanceMonitor instance...');
                const performanceMonitor = new PerformanceMonitor();
                log('✓ PerformanceMonitor instance created', 'success');
                
                log('Creating InputManager instance...');
                const inputManager = new InputManager();
                log('✓ InputManager instance created', 'success');
                
                log('Creating WebSocketClient instance...');
                const networkClient = new WebSocketClient();
                log('✓ WebSocketClient instance created', 'success');
                
                // Load and test systems one by one
                log('Loading PlayerSystem...');
                await loadScript('game/systems/player.js');
                log('✓ PlayerSystem loaded', 'success');
                
                log('Creating PlayerSystem instance...');
                const playerSystem = new PlayerSystem(gameState, inputManager, networkClient);
                log('✓ PlayerSystem instance created', 'success');
                
                log('Loading MapSystem...');
                await loadScript('game/systems/map.js');
                log('✓ MapSystem loaded', 'success');
                
                log('Creating MapSystem instance...');
                const mapSystem = new MapSystem(gameState);
                log('✓ MapSystem instance created', 'success');
                
                log('Loading BombSystem...');
                await loadScript('game/systems/bomb.js');
                log('✓ BombSystem loaded', 'success');
                
                log('Creating BombSystem instance...');
                const bombSystem = new BombSystem(gameState, mapSystem, playerSystem, networkClient);
                log('✓ BombSystem instance created', 'success');
                
                log('Loading PowerupSystem...');
                await loadScript('game/systems/powerup.js');
                log('✓ PowerupSystem loaded', 'success');
                
                log('Creating PowerupSystem instance...');
                const powerupSystem = new PowerupSystem(gameState, playerSystem);
                log('✓ PowerupSystem instance created', 'success');
                
                // Load components
                log('Loading LobbyComponent...');
                await loadScript('game/components/lobby.js');
                log('✓ LobbyComponent loaded', 'success');
                
                log('Creating LobbyComponent instance...');
                const container = document.getElementById('app');
                const lobbyComponent = new LobbyComponent(container);
                log('✓ LobbyComponent instance created', 'success');
                
                log('🎉 All systems loaded and created successfully!', 'success');
                log('Now testing initialization...', 'warning');
                
                // Test initialization
                performanceMonitor.init();
                log('✓ PerformanceMonitor initialized', 'success');
                
                inputManager.init();
                log('✓ InputManager initialized', 'success');
                
                playerSystem.init();
                log('✓ PlayerSystem initialized', 'success');
                
                mapSystem.init();
                log('✓ MapSystem initialized', 'success');
                
                bombSystem.init();
                log('✓ BombSystem initialized', 'success');
                
                powerupSystem.init();
                log('✓ PowerupSystem initialized', 'success');
                
                log('🎉 All systems initialized successfully!', 'success');
                
                // Test rendering
                log('Testing lobby render...', 'warning');
                lobbyComponent.render();
                log('✓ Lobby rendered successfully!', 'success');
                
                log('🎉 ALL TESTS PASSED! The issue is not in the individual systems.', 'success');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
