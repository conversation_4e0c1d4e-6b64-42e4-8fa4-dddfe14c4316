/**
 * Bomb System
 * Handles bomb placement, explosions, and damage
 */

class BombSystem {
    constructor(gameState, mapSystem, playerSystem) {
        this.gameState = gameState;
        this.mapSystem = mapSystem;
        this.playerSystem = playerSystem;
        
        // Explosion directions
        this.explosionDirections = [
            { x: 0, y: 0, name: 'center' },
            { x: 1, y: 0, name: 'right' },
            { x: -1, y: 0, name: 'left' },
            { x: 0, y: 1, name: 'down' },
            { x: 0, y: -1, name: 'up' }
        ];
        
        // Active timers
        this.bombTimers = new Map();
        this.explosionTimers = new Map();
        
        // Bind methods
        this.update = this.update.bind(this);
        this.createBomb = this.createBomb.bind(this);
        this.explodeBomb = this.explodeBomb.bind(this);
        this.createExplosion = this.createExplosion.bind(this);
        this.checkExplosionDamage = this.checkExplosionDamage.bind(this);
        this.handleNetworkBomb = this.handleNetworkBomb.bind(this);
    }
    
    /**
     * Initialize bomb system
     */
    init() {
        // Listen for network bomb events
        if (this.gameState.networkClient) {
            this.gameState.networkClient.on('bomb_placed', this.handleNetworkBomb);
        }
        
        console.log('Bomb system initialized');
    }
    
    /**
     * Update bomb system
     */
    update(deltaTime) {
        const currentTime = Date.now();
        
        // Update bomb timers
        for (const [bombId, bomb] of this.gameState.bombs) {
            const elapsed = currentTime - bomb.startTime;
            
            if (elapsed >= bomb.timer) {
                this.explodeBomb(bombId);
            } else {
                // Update bomb visual (pulsing effect)
                this.updateBombVisual(bomb, elapsed / bomb.timer);
            }
        }
        
        // Update explosion timers
        for (const [explosionId, explosion] of this.gameState.explosions) {
            const elapsed = currentTime - explosion.startTime;
            
            if (elapsed >= explosion.duration) {
                this.removeExplosion(explosionId);
            }
        }
    }
    
    /**
     * Handle network bomb placement
     */
    handleNetworkBomb(data) {
        this.createBomb({
            playerId: data.playerId,
            x: data.x,
            y: data.y,
            power: data.power
        });
    }
    
    /**
     * Create a bomb
     */
    createBomb(bombData) {
        const bomb = this.gameState.addBomb(bombData);
        
        // Create bomb visual element
        this.createBombElement(bomb);
        
        console.log(`Bomb placed by player ${bomb.playerId} at (${bomb.x}, ${bomb.y})`);
        return bomb;
    }
    
    /**
     * Create bomb DOM element
     */
    createBombElement(bomb) {
        const container = DOM.query('.game-grid');
        if (!container) return;
        
        const element = DOM.createElement('div', {
            className: 'bomb',
            style: {
                position: 'absolute',
                left: bomb.x + 'px',
                top: bomb.y + 'px',
                width: '30px',
                height: '30px',
                zIndex: '5'
            }
        });
        
        container.appendChild(element);
        bomb.element = element;
        
        return element;
    }
    
    /**
     * Update bomb visual (pulsing animation)
     */
    updateBombVisual(bomb, progress) {
        if (!bomb.element) return;
        
        // Increase pulsing speed as bomb gets closer to explosion
        const pulseSpeed = 1 + progress * 3;
        const scale = 1 + Math.sin(Date.now() * 0.01 * pulseSpeed) * 0.1;
        
        bomb.element.style.transform = `scale(${scale})`;
        
        // Change color as it gets closer to explosion
        const red = Math.floor(255 * (0.5 + progress * 0.5));
        bomb.element.style.backgroundColor = `rgb(${red}, 51, 51)`;
    }
    
    /**
     * Explode a bomb
     */
    explodeBomb(bombId) {
        const bomb = this.gameState.bombs.get(bombId);
        if (!bomb) return;
        
        console.log(`Bomb ${bombId} exploding!`);
        
        // Get bomb grid position
        const gridX = Math.floor(bomb.x / this.gameState.settings.cellSize);
        const gridY = Math.floor(bomb.y / this.gameState.settings.cellSize);
        
        // Create explosions in all directions
        this.createExplosionPattern(gridX, gridY, bomb.power);
        
        // Remove bomb
        this.removeBomb(bombId);
        
        // Check for chain reactions (other bombs in explosion range)
        this.checkChainReactions(gridX, gridY, bomb.power);
    }
    
    /**
     * Create explosion pattern
     */
    createExplosionPattern(centerX, centerY, power) {
        const explosions = [];
        
        // Center explosion
        explosions.push(this.createExplosion(centerX, centerY, 'center'));
        
        // Directional explosions
        for (const direction of this.explosionDirections.slice(1)) { // Skip center
            for (let i = 1; i <= power; i++) {
                const x = centerX + direction.x * i;
                const y = centerY + direction.y * i;
                
                // Check bounds
                if (x < 0 || y < 0 || x >= this.gameState.settings.mapWidth || y >= this.gameState.settings.mapHeight) {
                    break;
                }
                
                // Check for walls (stop explosion)
                const cell = this.gameState.getCell(x, y);
                if (cell === 1) { // Wall
                    break;
                }
                
                // Create explosion
                explosions.push(this.createExplosion(x, y, direction.name));
                
                // Check for blocks (destroy and stop explosion)
                if (cell === 2) { // Block
                    this.mapSystem.destroyBlock(x, y);
                    break;
                }
            }
        }
        
        // Check for damage to players and powerups
        setTimeout(() => {
            this.checkExplosionDamage(explosions);
        }, 50); // Small delay to ensure explosions are created
        
        return explosions;
    }
    
    /**
     * Create single explosion
     */
    createExplosion(gridX, gridY, direction) {
        const pixelX = gridX * this.gameState.settings.cellSize;
        const pixelY = gridY * this.gameState.settings.cellSize;
        
        const explosion = this.gameState.addExplosion({
            x: pixelX,
            y: pixelY,
            direction: direction
        });
        
        // Create explosion visual
        this.createExplosionElement(explosion);
        
        return explosion;
    }
    
    /**
     * Create explosion DOM element
     */
    createExplosionElement(explosion) {
        const container = DOM.query('.game-grid');
        if (!container) return;
        
        const element = DOM.createElement('div', {
            className: `explosion explosion-${explosion.direction}`,
            style: {
                position: 'absolute',
                left: explosion.x + 'px',
                top: explosion.y + 'px',
                width: this.gameState.settings.cellSize + 'px',
                height: this.gameState.settings.cellSize + 'px',
                zIndex: '8'
            }
        });
        
        container.appendChild(element);
        explosion.element = element;
        
        return element;
    }
    
    /**
     * Check for chain reactions (bombs in explosion range)
     */
    checkChainReactions(centerX, centerY, power) {
        for (const [bombId, bomb] of this.gameState.bombs) {
            const bombGridX = Math.floor(bomb.x / this.gameState.settings.cellSize);
            const bombGridY = Math.floor(bomb.y / this.gameState.settings.cellSize);
            
            // Check if bomb is in explosion range
            if (this.isInExplosionRange(centerX, centerY, power, bombGridX, bombGridY)) {
                // Trigger chain explosion with a small delay
                setTimeout(() => {
                    if (this.gameState.bombs.has(bombId)) {
                        this.explodeBomb(bombId);
                    }
                }, 100);
            }
        }
    }
    
    /**
     * Check if position is in explosion range
     */
    isInExplosionRange(centerX, centerY, power, targetX, targetY) {
        // Same position
        if (centerX === targetX && centerY === targetY) {
            return true;
        }
        
        // Check horizontal line
        if (centerY === targetY) {
            const distance = Math.abs(centerX - targetX);
            if (distance <= power) {
                // Check for walls blocking the explosion
                const startX = Math.min(centerX, targetX);
                const endX = Math.max(centerX, targetX);
                
                for (let x = startX + 1; x < endX; x++) {
                    if (this.gameState.getCell(x, centerY) === 1) {
                        return false; // Wall blocks explosion
                    }
                }
                return true;
            }
        }
        
        // Check vertical line
        if (centerX === targetX) {
            const distance = Math.abs(centerY - targetY);
            if (distance <= power) {
                // Check for walls blocking the explosion
                const startY = Math.min(centerY, targetY);
                const endY = Math.max(centerY, targetY);
                
                for (let y = startY + 1; y < endY; y++) {
                    if (this.gameState.getCell(centerX, y) === 1) {
                        return false; // Wall blocks explosion
                    }
                }
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check explosion damage to players and powerups
     */
    checkExplosionDamage(explosions) {
        for (const explosion of explosions) {
            const gridX = Math.floor(explosion.x / this.gameState.settings.cellSize);
            const gridY = Math.floor(explosion.y / this.gameState.settings.cellSize);
            
            // Check player damage
            for (const player of this.gameState.getAllPlayers()) {
                if (!player.isAlive) continue;
                
                const playerGridX = Math.floor((player.x + 18) / this.gameState.settings.cellSize); // Center of player
                const playerGridY = Math.floor((player.y + 18) / this.gameState.settings.cellSize);
                
                if (playerGridX === gridX && playerGridY === gridY) {
                    this.playerSystem.takeDamage(player);
                    console.log(`Player ${player.nickname} hit by explosion!`);
                }
            }
            
            // Check powerup destruction
            for (const [powerupId, powerup] of this.gameState.powerups) {
                const powerupGridX = Math.floor(powerup.x / this.gameState.settings.cellSize);
                const powerupGridY = Math.floor(powerup.y / this.gameState.settings.cellSize);
                
                if (powerupGridX === gridX && powerupGridY === gridY) {
                    this.removePowerup(powerupId);
                    console.log(`Powerup destroyed by explosion!`);
                }
            }
        }
    }
    
    /**
     * Remove bomb
     */
    removeBomb(bombId) {
        const bomb = this.gameState.bombs.get(bombId);
        if (bomb && bomb.element) {
            DOM.remove(bomb.element);
        }
        
        this.gameState.removeBomb(bombId);
        
        if (this.bombTimers.has(bombId)) {
            clearTimeout(this.bombTimers.get(bombId));
            this.bombTimers.delete(bombId);
        }
    }
    
    /**
     * Remove explosion
     */
    removeExplosion(explosionId) {
        const explosion = this.gameState.explosions.get(explosionId);
        if (explosion && explosion.element) {
            DOM.remove(explosion.element);
        }
        
        this.gameState.removeExplosion(explosionId);
        
        if (this.explosionTimers.has(explosionId)) {
            clearTimeout(this.explosionTimers.get(explosionId));
            this.explosionTimers.delete(explosionId);
        }
    }
    
    /**
     * Remove powerup (called when destroyed by explosion)
     */
    removePowerup(powerupId) {
        const powerup = this.gameState.powerups.get(powerupId);
        if (powerup && powerup.element) {
            DOM.remove(powerup.element);
        }
        
        this.gameState.removePowerup(powerupId);
    }
    
    /**
     * Cleanup bomb system
     */
    cleanup() {
        // Clear all timers
        for (const timer of this.bombTimers.values()) {
            clearTimeout(timer);
        }
        for (const timer of this.explosionTimers.values()) {
            clearTimeout(timer);
        }
        
        this.bombTimers.clear();
        this.explosionTimers.clear();
        
        // Remove all bomb and explosion elements
        for (const bomb of this.gameState.bombs.values()) {
            if (bomb.element) {
                DOM.remove(bomb.element);
            }
        }
        
        for (const explosion of this.gameState.explosions.values()) {
            if (explosion.element) {
                DOM.remove(explosion.element);
            }
        }
    }
}

// Export for use in other modules
window.BombSystem = BombSystem;
