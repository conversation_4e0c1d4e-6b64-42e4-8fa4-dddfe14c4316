package main

import (
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Player represents a game player
type Player struct {
	ID       string          `json:"id"`
	Nickname string          `json:"nickname"`
	X        float64         `json:"x"`
	Y        float64         `json:"y"`
	Lives    int             `json:"lives"`
	Powerups Powerups        `json:"powerups"`
	IsAlive  bool            `json:"isAlive"`
	Conn     *websocket.Conn `json:"-"`
}

// Powerups represents player power-ups
type Powerups struct {
	Bombs  int `json:"bombs"`
	Flames int `json:"flames"`
	Speed  int `json:"speed"`
}

// GameState represents the current game state
type GameState struct {
	Status             string             `json:"status"` // waiting, countdown, playing, finished
	Players            map[string]*Player `json:"players"`
	MaxPlayers         int                `json:"maxPlayers"`
	MinPlayers         int                `json:"minPlayers"`
	WaitTime           time.Duration      `json:"-"`
	CountdownTime      time.Duration      `json:"-"`
	GameStartTime      *time.Time         `json:"gameStartTime"`
	CountdownStartTime *time.Time         `json:"countdownStartTime"`
	mutex              sync.RWMutex
}

// Message represents a WebSocket message
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// Server represents the game server
type Server struct {
	gameState      *GameState
	upgrader       websocket.Upgrader
	waitTimer      *time.Timer
	countdownTimer *time.Timer
}

// NewServer creates a new game server
func NewServer() *Server {
	return &Server{
		gameState: &GameState{
			Status:        "waiting",
			Players:       make(map[string]*Player),
			MaxPlayers:    4,
			MinPlayers:    2,
			WaitTime:      20 * time.Second,
			CountdownTime: 10 * time.Second,
		},
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for development
			},
		},
	}
}

func main() {
	server := NewServer()

	// Serve static files
	fs := http.FileServer(http.Dir("."))
	http.Handle("/", fs)

	// WebSocket endpoint
	http.HandleFunc("/ws", server.handleWebSocket)

	port := ":8080"
	fmt.Printf("Bomberman server running on http://localhost%s\n", port)
	fmt.Printf("Open http://localhost%s to play\n", port)

	log.Fatal(http.ListenAndServe(port, nil))
}

func (s *Server) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}
	defer conn.Close()

	log.Println("New WebSocket connection")

	for {
		var msg Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("WebSocket read error: %v", err)
			s.handleDisconnection(conn)
			break
		}

		s.handleMessage(conn, msg)
	}
}

func (s *Server) handleMessage(conn *websocket.Conn, msg Message) {
	switch msg.Type {
	case "join":
		s.handlePlayerJoin(conn, msg.Data)
	case "chat":
		s.handleChatMessage(conn, msg.Data)
	case "player_move":
		s.handlePlayerMove(conn, msg.Data)
	case "place_bomb":
		s.handlePlaceBomb(conn, msg.Data)
	case "ping":
		s.sendToClient(conn, Message{
			Type:      "pong",
			Timestamp: time.Now().UnixMilli(),
		})
	default:
		log.Printf("Unknown message type: %s", msg.Type)
	}
}

func (s *Server) handlePlayerJoin(conn *websocket.Conn, data interface{}) {
	s.gameState.mutex.Lock()
	defer s.gameState.mutex.Unlock()

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		s.sendError(conn, "Invalid join data")
		return
	}

	nickname, ok := dataMap["nickname"].(string)
	if !ok {
		s.sendError(conn, "Invalid nickname")
		return
	}

	// Check if game is full
	if len(s.gameState.Players) >= s.gameState.MaxPlayers {
		s.sendError(conn, "Game is full")
		return
	}

	// Check if game is already in progress
	if s.gameState.Status == "playing" {
		s.sendError(conn, "Game is already in progress")
		return
	}

	// Create player
	playerID := s.generatePlayerID()
	player := &Player{
		ID:       playerID,
		Nickname: nickname,
		X:        0,
		Y:        0,
		Lives:    3,
		Powerups: Powerups{Bombs: 1, Flames: 1, Speed: 1},
		IsAlive:  true,
		Conn:     conn,
	}

	s.gameState.Players[playerID] = player

	log.Printf("Player %s joined (%s)", nickname, playerID)

	// Send join confirmation
	s.sendToClient(conn, Message{
		Type: "join_success",
		Data: map[string]interface{}{
			"playerId":    playerID,
			"playerCount": len(s.gameState.Players),
			"maxPlayers":  s.gameState.MaxPlayers,
		},
	})

	// Broadcast player count update
	s.broadcastPlayerCount()

	// Check if we should start countdown
	s.checkGameStart()
}

func (s *Server) handleChatMessage(conn *websocket.Conn, data interface{}) {
	player := s.getPlayerByConn(conn)
	if player == nil {
		return
	}

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}

	message, ok := dataMap["message"].(string)
	if !ok {
		return
	}

	chatMessage := Message{
		Type: "chat_message",
		Data: map[string]interface{}{
			"playerId":  player.ID,
			"nickname":  player.Nickname,
			"message":   message,
			"timestamp": time.Now().UnixMilli(),
		},
	}

	s.broadcast(chatMessage)
}

func (s *Server) handlePlayerMove(conn *websocket.Conn, data interface{}) {
	player := s.getPlayerByConn(conn)
	if player == nil || !player.IsAlive {
		return
	}

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}

	if x, ok := dataMap["x"].(float64); ok {
		player.X = x
	}
	if y, ok := dataMap["y"].(float64); ok {
		player.Y = y
	}

	// Broadcast position update
	s.broadcast(Message{
		Type: "player_moved",
		Data: map[string]interface{}{
			"playerId": player.ID,
			"x":        player.X,
			"y":        player.Y,
		},
	})
}

func (s *Server) handlePlaceBomb(conn *websocket.Conn, data interface{}) {
	player := s.getPlayerByConn(conn)
	if player == nil || !player.IsAlive {
		return
	}

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}

	x, xOk := dataMap["x"].(float64)
	y, yOk := dataMap["y"].(float64)

	if !xOk || !yOk {
		return
	}

	// Broadcast bomb placement
	s.broadcast(Message{
		Type: "bomb_placed",
		Data: map[string]interface{}{
			"playerId": player.ID,
			"x":        x,
			"y":        y,
			"power":    player.Powerups.Flames,
		},
	})
}

func (s *Server) handleDisconnection(conn *websocket.Conn) {
	s.gameState.mutex.Lock()
	defer s.gameState.mutex.Unlock()

	player := s.getPlayerByConn(conn)
	if player != nil {
		log.Printf("Player %s disconnected", player.Nickname)
		delete(s.gameState.Players, player.ID)

		// Broadcast player count update
		s.broadcastPlayerCount()

		// Reset game if not enough players
		if len(s.gameState.Players) < s.gameState.MinPlayers {
			s.resetGame()
		}
	}
}

func (s *Server) checkGameStart() {
	playerCount := len(s.gameState.Players)

	if playerCount >= s.gameState.MaxPlayers {
		// Start countdown immediately if full
		s.startCountdown()
	} else if playerCount >= s.gameState.MinPlayers && s.waitTimer == nil {
		// Start wait timer if minimum players reached
		s.startWaitTimer()
	}
}

func (s *Server) startWaitTimer() {
	log.Println("Starting wait timer...")
	s.waitTimer = time.AfterFunc(s.gameState.WaitTime, func() {
		s.gameState.mutex.Lock()
		defer s.gameState.mutex.Unlock()

		if len(s.gameState.Players) >= s.gameState.MinPlayers {
			s.startCountdown()
		}
	})
}

func (s *Server) startCountdown() {
	log.Println("Starting countdown...")
	s.gameState.Status = "countdown"
	now := time.Now()
	s.gameState.CountdownStartTime = &now

	// Clear wait timer if it exists
	if s.waitTimer != nil {
		s.waitTimer.Stop()
		s.waitTimer = nil
	}

	// Broadcast countdown start
	s.broadcast(Message{
		Type: "countdown_start",
		Data: map[string]interface{}{
			"duration": s.gameState.CountdownTime.Milliseconds(),
		},
	})

	// Start game after countdown
	s.countdownTimer = time.AfterFunc(s.gameState.CountdownTime, func() {
		s.startGame()
	})
}

func (s *Server) startGame() {
	s.gameState.mutex.Lock()
	defer s.gameState.mutex.Unlock()

	log.Println("Starting game...")
	s.gameState.Status = "playing"
	now := time.Now()
	s.gameState.GameStartTime = &now

	// Assign starting positions
	s.assignStartingPositions()

	// Prepare players data
	players := make([]map[string]interface{}, 0, len(s.gameState.Players))
	for _, player := range s.gameState.Players {
		players = append(players, map[string]interface{}{
			"id":       player.ID,
			"nickname": player.Nickname,
			"x":        player.X,
			"y":        player.Y,
			"lives":    player.Lives,
			"powerups": player.Powerups,
		})
	}

	// Broadcast game start
	s.broadcast(Message{
		Type: "game_start",
		Data: map[string]interface{}{
			"players": players,
		},
	})
}

func (s *Server) assignStartingPositions() {
	positions := []map[string]float64{
		{"x": 1, "y": 1},  // Top-left
		{"x": 13, "y": 1}, // Top-right
		{"x": 1, "y": 9},  // Bottom-left
		{"x": 13, "y": 9}, // Bottom-right
	}

	i := 0
	for _, player := range s.gameState.Players {
		pos := positions[i%len(positions)]
		player.X = pos["x"]
		player.Y = pos["y"]
		i++
	}
}

func (s *Server) resetGame() {
	log.Println("Resetting game...")
	s.gameState.Status = "waiting"
	s.gameState.GameStartTime = nil
	s.gameState.CountdownStartTime = nil

	// Clear timers
	if s.waitTimer != nil {
		s.waitTimer.Stop()
		s.waitTimer = nil
	}
	if s.countdownTimer != nil {
		s.countdownTimer.Stop()
		s.countdownTimer = nil
	}

	// Broadcast reset
	s.broadcast(Message{Type: "game_reset"})
}

func (s *Server) broadcastPlayerCount() {
	s.broadcast(Message{
		Type: "player_count_update",
		Data: map[string]interface{}{
			"playerCount": len(s.gameState.Players),
			"maxPlayers":  s.gameState.MaxPlayers,
		},
	})
}

func (s *Server) broadcast(message Message) {
	s.gameState.mutex.RLock()
	defer s.gameState.mutex.RUnlock()

	for _, player := range s.gameState.Players {
		s.sendToClient(player.Conn, message)
	}
}

func (s *Server) sendToClient(conn *websocket.Conn, message Message) {
	if conn != nil {
		err := conn.WriteJSON(message)
		if err != nil {
			log.Printf("Error sending message: %v", err)
		}
	}
}

func (s *Server) sendError(conn *websocket.Conn, errorMsg string) {
	s.sendToClient(conn, Message{
		Type:      "join_error",
		Data:      map[string]string{"message": errorMsg},
		Timestamp: time.Now().UnixMilli(),
	})
}

func (s *Server) getPlayerByConn(conn *websocket.Conn) *Player {
	s.gameState.mutex.RLock()
	defer s.gameState.mutex.RUnlock()

	for _, player := range s.gameState.Players {
		if player.Conn == conn {
			return player
		}
	}
	return nil
}

func (s *Server) generatePlayerID() string {
	return fmt.Sprintf("player_%d_%d", time.Now().UnixNano(), rand.Intn(1000))
}
