/**
 * Mini Framework - Game Engine
 * Core game loop and performance management for 60fps gaming
 */

class GameEngine {
    constructor() {
        this.isRunning = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.targetFPS = 60;
        this.frameTime = 1000 / this.targetFPS;
        
        // Performance tracking
        this.frameCount = 0;
        this.fpsCounter = 0;
        this.lastFPSUpdate = 0;
        this.performanceData = {
            fps: 0,
            frameTime: 0,
            updateTime: 0,
            renderTime: 0
        };
        
        // Game systems
        this.systems = new Map();
        this.entities = new Set();
        
        // Callbacks
        this.updateCallback = null;
        this.renderCallback = null;
        this.fpsCallback = null;
        
        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
        this.start = this.start.bind(this);
        this.stop = this.stop.bind(this);
    }
    
    /**
     * Start the game engine
     */
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.lastTime = performance.now();
        this.lastFPSUpdate = this.lastTime;
        
        console.log('Game Engine started - Target FPS:', this.targetFPS);
        requestAnimationFrame(this.gameLoop);
    }
    
    /**
     * Stop the game engine
     */
    stop() {
        this.isRunning = false;
        console.log('Game Engine stopped');
    }
    
    /**
     * Main game loop using requestAnimationFrame
     */
    gameLoop(currentTime) {
        if (!this.isRunning) return;
        
        // Calculate delta time
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Performance tracking
        const updateStart = performance.now();
        
        // Update game logic
        this.update(this.deltaTime);
        
        const updateEnd = performance.now();
        const renderStart = updateEnd;
        
        // Render game
        this.render(this.deltaTime);
        
        const renderEnd = performance.now();
        
        // Update performance data
        this.performanceData.updateTime = updateEnd - updateStart;
        this.performanceData.renderTime = renderEnd - renderStart;
        this.performanceData.frameTime = this.deltaTime;
        
        // Calculate FPS
        this.frameCount++;
        if (currentTime - this.lastFPSUpdate >= 1000) {
            this.performanceData.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFPSUpdate = currentTime;
            
            // Call FPS callback if provided
            if (this.fpsCallback) {
                this.fpsCallback(this.performanceData);
            }
            
            // Warn if FPS is too low
            if (this.performanceData.fps < this.targetFPS * 0.9) {
                console.warn(`Low FPS detected: ${this.performanceData.fps}`);
            }
        }
        
        // Continue the loop
        requestAnimationFrame(this.gameLoop);
    }
    
    /**
     * Update game logic
     */
    update(deltaTime) {
        // Update all systems
        for (const [name, system] of this.systems) {
            if (system.update && typeof system.update === 'function') {
                system.update(deltaTime);
            }
        }
        
        // Call custom update callback
        if (this.updateCallback) {
            this.updateCallback(deltaTime);
        }
    }
    
    /**
     * Render game
     */
    render(deltaTime) {
        // Render all systems
        for (const [name, system] of this.systems) {
            if (system.render && typeof system.render === 'function') {
                system.render(deltaTime);
            }
        }
        
        // Call custom render callback
        if (this.renderCallback) {
            this.renderCallback(deltaTime);
        }
    }
    
    /**
     * Add a system to the engine
     */
    addSystem(name, system) {
        this.systems.set(name, system);
        
        // Initialize system if it has an init method
        if (system.init && typeof system.init === 'function') {
            system.init();
        }
    }
    
    /**
     * Remove a system from the engine
     */
    removeSystem(name) {
        const system = this.systems.get(name);
        if (system) {
            // Cleanup system if it has a cleanup method
            if (system.cleanup && typeof system.cleanup === 'function') {
                system.cleanup();
            }
            this.systems.delete(name);
        }
    }
    
    /**
     * Get a system by name
     */
    getSystem(name) {
        return this.systems.get(name);
    }
    
    /**
     * Set update callback
     */
    onUpdate(callback) {
        this.updateCallback = callback;
    }
    
    /**
     * Set render callback
     */
    onRender(callback) {
        this.renderCallback = callback;
    }
    
    /**
     * Set FPS callback for performance monitoring
     */
    onFPS(callback) {
        this.fpsCallback = callback;
    }
    
    /**
     * Get current performance data
     */
    getPerformanceData() {
        return { ...this.performanceData };
    }
}

// Export for use in other modules
window.GameEngine = GameEngine;
