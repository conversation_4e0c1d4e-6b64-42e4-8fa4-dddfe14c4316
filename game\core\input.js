/**
 * Input Manager
 * Handles keyboard and mouse input for the game
 */

class InputManager {
    constructor() {
        this.keys = new Map();
        this.keysPressed = new Map();
        this.keysReleased = new Map();
        this.mouse = {
            x: 0,
            y: 0,
            buttons: new Map(),
            buttonsPressed: new Map(),
            buttonsReleased: new Map()
        };
        
        // Key mappings for players
        this.keyMappings = {
            player1: {
                up: 'ArrowUp',
                down: 'ArrowDown',
                left: 'ArrowLeft',
                right: 'ArrowRight',
                bomb: 'Space'
            },
            player2: {
                up: 'KeyW',
                down: 'KeyS',
                left: 'KeyA',
                right: 'KeyD',
                bomb: 'KeyQ'
            },
            player3: {
                up: 'KeyI',
                down: 'KeyK',
                left: 'KeyJ',
                right: 'KeyL',
                bomb: 'KeyU'
            },
            player4: {
                up: 'Numpad8',
                down: 'Numpad5',
                left: 'Numpad4',
                right: 'Numpad6',
                bomb: 'Numpad0'
            }
        };
        
        // Input handlers
        this.handlers = new Map();
        
        // Bind methods
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.update = this.update.bind(this);
        
        this.init();
    }
    
    /**
     * Initialize input manager
     */
    init() {
        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
        
        // Mouse events
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mousedown', this.handleMouseDown);
        document.addEventListener('mouseup', this.handleMouseUp);
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', (e) => e.preventDefault());
        
        console.log('Input manager initialized');
    }
    
    /**
     * Handle key down events
     */
    handleKeyDown(event) {
        const key = event.code;
        
        // Prevent default for game keys
        if (this.isGameKey(key)) {
            event.preventDefault();
        }
        
        // Track key state
        if (!this.keys.get(key)) {
            this.keysPressed.set(key, true);
        }
        this.keys.set(key, true);
        
        // Emit key down event
        this.emit('keydown', { key, event });
    }
    
    /**
     * Handle key up events
     */
    handleKeyUp(event) {
        const key = event.code;
        
        // Prevent default for game keys
        if (this.isGameKey(key)) {
            event.preventDefault();
        }
        
        // Track key state
        this.keys.set(key, false);
        this.keysReleased.set(key, true);
        
        // Emit key up event
        this.emit('keyup', { key, event });
    }
    
    /**
     * Handle mouse move events
     */
    handleMouseMove(event) {
        this.mouse.x = event.clientX;
        this.mouse.y = event.clientY;
        
        this.emit('mousemove', { x: this.mouse.x, y: this.mouse.y, event });
    }
    
    /**
     * Handle mouse down events
     */
    handleMouseDown(event) {
        const button = event.button;
        
        if (!this.mouse.buttons.get(button)) {
            this.mouse.buttonsPressed.set(button, true);
        }
        this.mouse.buttons.set(button, true);
        
        this.emit('mousedown', { button, x: this.mouse.x, y: this.mouse.y, event });
    }
    
    /**
     * Handle mouse up events
     */
    handleMouseUp(event) {
        const button = event.button;
        
        this.mouse.buttons.set(button, false);
        this.mouse.buttonsReleased.set(button, true);
        
        this.emit('mouseup', { button, x: this.mouse.x, y: this.mouse.y, event });
    }
    
    /**
     * Update input state (call once per frame)
     */
    update() {
        // Clear pressed/released states
        this.keysPressed.clear();
        this.keysReleased.clear();
        this.mouse.buttonsPressed.clear();
        this.mouse.buttonsReleased.clear();
    }
    
    /**
     * Check if key is currently pressed
     */
    isKeyDown(key) {
        return this.keys.get(key) || false;
    }
    
    /**
     * Check if key was just pressed this frame
     */
    isKeyPressed(key) {
        return this.keysPressed.get(key) || false;
    }
    
    /**
     * Check if key was just released this frame
     */
    isKeyReleased(key) {
        return this.keysReleased.get(key) || false;
    }
    
    /**
     * Check if mouse button is currently pressed
     */
    isMouseDown(button) {
        return this.mouse.buttons.get(button) || false;
    }
    
    /**
     * Check if mouse button was just pressed this frame
     */
    isMousePressed(button) {
        return this.mouse.buttonsPressed.get(button) || false;
    }
    
    /**
     * Check if mouse button was just released this frame
     */
    isMouseReleased(button) {
        return this.mouse.buttonsReleased.get(button) || false;
    }
    
    /**
     * Get player input state
     */
    getPlayerInput(playerKey = 'player1') {
        const mapping = this.keyMappings[playerKey];
        if (!mapping) return null;
        
        return {
            up: this.isKeyDown(mapping.up),
            down: this.isKeyDown(mapping.down),
            left: this.isKeyDown(mapping.left),
            right: this.isKeyDown(mapping.right),
            bomb: this.isKeyPressed(mapping.bomb),
            
            // Movement vector
            moveX: (this.isKeyDown(mapping.right) ? 1 : 0) - (this.isKeyDown(mapping.left) ? 1 : 0),
            moveY: (this.isKeyDown(mapping.down) ? 1 : 0) - (this.isKeyDown(mapping.up) ? 1 : 0)
        };
    }
    
    /**
     * Check if a key is used by the game
     */
    isGameKey(key) {
        for (const mapping of Object.values(this.keyMappings)) {
            if (Object.values(mapping).includes(key)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get mouse position
     */
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }
    
    /**
     * Get mouse position relative to an element
     */
    getMousePositionRelative(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: this.mouse.x - rect.left,
            y: this.mouse.y - rect.top
        };
    }
    
    /**
     * Set custom key mapping for a player
     */
    setPlayerKeyMapping(playerKey, mapping) {
        this.keyMappings[playerKey] = { ...mapping };
    }
    
    /**
     * Event system methods
     */
    on(event, handler) {
        if (!this.handlers.has(event)) {
            this.handlers.set(event, []);
        }
        this.handlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.handlers.has(event)) {
            const handlers = this.handlers.get(event);
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.handlers.has(event)) {
            this.handlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in input handler for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * Cleanup input manager
     */
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mousedown', this.handleMouseDown);
        document.removeEventListener('mouseup', this.handleMouseUp);
        
        this.keys.clear();
        this.keysPressed.clear();
        this.keysReleased.clear();
        this.mouse.buttons.clear();
        this.mouse.buttonsPressed.clear();
        this.mouse.buttonsReleased.clear();
        this.handlers.clear();
        
        console.log('Input manager destroyed');
    }
}

// Export for use in other modules
window.InputManager = InputManager;
