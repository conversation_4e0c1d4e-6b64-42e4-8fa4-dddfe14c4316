/**
 * Game State Management
 * Central state management for the Bomberman game
 */

class GameState {
    constructor() {
        this.state = 'lobby'; // lobby, waiting, countdown, playing, finished
        this.players = new Map();
        this.bombs = new Map();
        this.explosions = new Map();
        this.powerups = new Map();
        this.map = null;
        
        // Game settings
        this.settings = {
            mapWidth: 15,
            mapHeight: 11,
            cellSize: 40,
            maxPlayers: 4,
            minPlayers: 2,
            playerLives: 3,
            bombTimer: 3000, // 3 seconds
            explosionDuration: 500 // 0.5 seconds
        };
        
        // Current player info
        this.currentPlayer = null;
        this.playerId = null;
        
        // Game timing
        this.gameStartTime = null;
        this.countdownStartTime = null;
        this.countdownDuration = 10000; // 10 seconds
        
        // Event system
        this.listeners = new Map();
        
        // Bind methods
        this.setState = this.setState.bind(this);
        this.addPlayer = this.addPlayer.bind(this);
        this.removePlayer = this.removePlayer.bind(this);
        this.updatePlayer = this.updatePlayer.bind(this);
        this.addBomb = this.addBomb.bind(this);
        this.removeBomb = this.removeBomb.bind(this);
        this.addExplosion = this.addExplosion.bind(this);
        this.removeExplosion = this.removeExplosion.bind(this);
        this.addPowerup = this.addPowerup.bind(this);
        this.removePowerup = this.removePowerup.bind(this);
    }
    
    /**
     * Set game state
     */
    setState(newState) {
        const oldState = this.state;
        this.state = newState;
        
        console.log(`Game state changed: ${oldState} -> ${newState}`);
        this.emit('stateChanged', { oldState, newState });
    }
    
    /**
     * Add player to game
     */
    addPlayer(playerData) {
        const player = {
            id: playerData.id,
            nickname: playerData.nickname,
            x: playerData.x || 0,
            y: playerData.y || 0,
            lives: playerData.lives || this.settings.playerLives,
            isAlive: true,
            powerups: {
                bombs: 1,
                flames: 1,
                speed: 1,
                ...playerData.powerups
            },
            activeBombs: 0,
            lastMoveTime: 0,
            element: null // Will be set by renderer
        };
        
        this.players.set(player.id, player);
        
        // Set as current player if it's our player
        if (player.id === this.playerId) {
            this.currentPlayer = player;
        }
        
        this.emit('playerAdded', player);
        return player;
    }
    
    /**
     * Remove player from game
     */
    removePlayer(playerId) {
        const player = this.players.get(playerId);
        if (player) {
            this.players.delete(playerId);
            this.emit('playerRemoved', player);
        }
    }
    
    /**
     * Update player data
     */
    updatePlayer(playerId, updates) {
        const player = this.players.get(playerId);
        if (player) {
            Object.assign(player, updates);
            this.emit('playerUpdated', player);
        }
    }
    
    /**
     * Get player by ID
     */
    getPlayer(playerId) {
        return this.players.get(playerId);
    }
    
    /**
     * Get all players
     */
    getAllPlayers() {
        return Array.from(this.players.values());
    }
    
    /**
     * Get alive players
     */
    getAlivePlayers() {
        return this.getAllPlayers().filter(player => player.isAlive);
    }
    
    /**
     * Add bomb to game
     */
    addBomb(bombData) {
        const bomb = {
            id: bombData.id || this.generateId('bomb'),
            playerId: bombData.playerId,
            x: bombData.x,
            y: bombData.y,
            power: bombData.power || 1,
            timer: bombData.timer || this.settings.bombTimer,
            startTime: Date.now(),
            element: null // Will be set by renderer
        };
        
        this.bombs.set(bomb.id, bomb);
        
        // Increment player's active bomb count
        const player = this.getPlayer(bomb.playerId);
        if (player) {
            player.activeBombs++;
        }
        
        this.emit('bombAdded', bomb);
        return bomb;
    }
    
    /**
     * Remove bomb from game
     */
    removeBomb(bombId) {
        const bomb = this.bombs.get(bombId);
        if (bomb) {
            this.bombs.delete(bombId);
            
            // Decrement player's active bomb count
            const player = this.getPlayer(bomb.playerId);
            if (player) {
                player.activeBombs = Math.max(0, player.activeBombs - 1);
            }
            
            this.emit('bombRemoved', bomb);
        }
    }
    
    /**
     * Add explosion to game
     */
    addExplosion(explosionData) {
        const explosion = {
            id: explosionData.id || this.generateId('explosion'),
            x: explosionData.x,
            y: explosionData.y,
            direction: explosionData.direction || 'center',
            startTime: Date.now(),
            duration: this.settings.explosionDuration,
            element: null // Will be set by renderer
        };
        
        this.explosions.set(explosion.id, explosion);
        this.emit('explosionAdded', explosion);
        return explosion;
    }
    
    /**
     * Remove explosion from game
     */
    removeExplosion(explosionId) {
        const explosion = this.explosions.get(explosionId);
        if (explosion) {
            this.explosions.delete(explosionId);
            this.emit('explosionRemoved', explosion);
        }
    }
    
    /**
     * Add powerup to game
     */
    addPowerup(powerupData) {
        const powerup = {
            id: powerupData.id || this.generateId('powerup'),
            type: powerupData.type, // 'bomb', 'flame', 'speed'
            x: powerupData.x,
            y: powerupData.y,
            element: null // Will be set by renderer
        };
        
        this.powerups.set(powerup.id, powerup);
        this.emit('powerupAdded', powerup);
        return powerup;
    }
    
    /**
     * Remove powerup from game
     */
    removePowerup(powerupId) {
        const powerup = this.powerups.get(powerupId);
        if (powerup) {
            this.powerups.delete(powerupId);
            this.emit('powerupRemoved', powerup);
        }
    }
    
    /**
     * Set map data
     */
    setMap(mapData) {
        this.map = mapData;
        this.emit('mapSet', mapData);
    }
    
    /**
     * Get cell at position
     */
    getCell(x, y) {
        if (!this.map || x < 0 || y < 0 || x >= this.settings.mapWidth || y >= this.settings.mapHeight) {
            return null;
        }
        return this.map[y][x];
    }
    
    /**
     * Set cell at position
     */
    setCell(x, y, value) {
        if (this.map && x >= 0 && y >= 0 && x < this.settings.mapWidth && y < this.settings.mapHeight) {
            this.map[y][x] = value;
            this.emit('cellChanged', { x, y, value });
        }
    }
    
    /**
     * Check if position is walkable
     */
    isWalkable(x, y) {
        const cell = this.getCell(x, y);
        return cell === 0; // 0 = empty, 1 = wall, 2 = block
    }
    
    /**
     * Convert pixel coordinates to grid coordinates
     */
    pixelToGrid(pixelX, pixelY) {
        return {
            x: Math.floor(pixelX / this.settings.cellSize),
            y: Math.floor(pixelY / this.settings.cellSize)
        };
    }
    
    /**
     * Convert grid coordinates to pixel coordinates
     */
    gridToPixel(gridX, gridY) {
        return {
            x: gridX * this.settings.cellSize,
            y: gridY * this.settings.cellSize
        };
    }
    
    /**
     * Generate unique ID
     */
    generateId(prefix = 'entity') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Event system methods
     */
    on(event, listener) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(listener);
    }
    
    off(event, listener) {
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * Reset game state
     */
    reset() {
        this.state = 'lobby';
        this.players.clear();
        this.bombs.clear();
        this.explosions.clear();
        this.powerups.clear();
        this.map = null;
        this.currentPlayer = null;
        this.playerId = null;
        this.gameStartTime = null;
        this.countdownStartTime = null;
        
        this.emit('gameReset');
    }
}

// Export for use in other modules
window.GameState = GameState;
