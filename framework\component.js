/**
 * Mini Framework - Component System
 * Base component class for creating reusable UI components
 */

class Component {
    constructor(container, props = {}) {
        this.container = container;
        this.props = props;
        this.element = null;
        this.children = [];
        this.state = {};
        this.isDestroyed = false;
        
        // Bind methods
        this.render = this.render.bind(this);
        this.update = this.update.bind(this);
        this.destroy = this.destroy.bind(this);
    }
    
    /**
     * Create the component's DOM structure
     * Override this method in subclasses
     */
    render() {
        // Default implementation creates a div
        this.element = DOM.createElement('div', {
            className: this.constructor.name.toLowerCase()
        });
        return this.element;
    }
    
    /**
     * Update component state and re-render if needed
     */
    setState(newState) {
        const prevState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        // Call update hook if state changed
        if (JSON.stringify(prevState) !== JSON.stringify(this.state)) {
            this.update(prevState);
        }
    }
    
    /**
     * Update hook called when state changes
     * Override this method in subclasses
     */
    update(prevState) {
        // Default implementation does nothing
    }
    
    /**
     * Mount component to container
     */
    mount() {
        if (!this.element) {
            this.render();
        }
        
        if (this.container && this.element) {
            this.container.appendChild(this.element);
        }
        
        // Call mounted hook
        this.onMounted();
    }
    
    /**
     * Unmount component from container
     */
    unmount() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        // Call unmounted hook
        this.onUnmounted();
    }
    
    /**
     * Destroy component and clean up resources
     */
    destroy() {
        if (this.isDestroyed) return;
        
        // Destroy all children first
        this.children.forEach(child => {
            if (child && typeof child.destroy === 'function') {
                child.destroy();
            }
        });
        this.children = [];
        
        // Unmount from DOM
        this.unmount();
        
        // Clean up references
        this.element = null;
        this.container = null;
        this.props = null;
        this.state = null;
        this.isDestroyed = true;
        
        // Call destroyed hook
        this.onDestroyed();
    }
    
    /**
     * Add child component
     */
    addChild(child) {
        if (child && !this.children.includes(child)) {
            this.children.push(child);
        }
    }
    
    /**
     * Remove child component
     */
    removeChild(child) {
        const index = this.children.indexOf(child);
        if (index !== -1) {
            this.children.splice(index, 1);
            if (child && typeof child.destroy === 'function') {
                child.destroy();
            }
        }
    }
    
    /**
     * Lifecycle hooks - override in subclasses
     */
    onMounted() {}
    onUnmounted() {}
    onDestroyed() {}
    
    /**
     * Get element by selector within this component
     */
    query(selector) {
        return this.element ? DOM.query(selector, this.element) : null;
    }
    
    /**
     * Get all elements by selector within this component
     */
    queryAll(selector) {
        return this.element ? DOM.queryAll(selector, this.element) : [];
    }
}

// Export for use in other modules
window.Component = Component;
