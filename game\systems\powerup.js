/**
 * Powerup System
 * Handles powerup spawning, collection, and effects
 */

class PowerupSystem {
    constructor(gameState, playerSystem) {
        this.gameState = gameState;
        this.playerSystem = playerSystem;
        
        // Powerup types and their properties
        this.powerupTypes = {
            bomb: {
                name: 'Bomb',
                description: 'Increases bomb capacity by 1',
                color: '#ff0000',
                icon: '💣'
            },
            flame: {
                name: 'Flame',
                description: 'Increases explosion range by 1',
                color: '#ff6600',
                icon: '🔥'
            },
            speed: {
                name: 'Speed',
                description: 'Increases movement speed',
                color: '#00ff00',
                icon: '⚡'
            }
        };
        
        // Spawn settings
        this.spawnChance = 0.3; // 30% chance when block is destroyed
        this.spawnWeights = {
            bomb: 0.4,   // 40% of powerups
            flame: 0.4,  // 40% of powerups
            speed: 0.2   // 20% of powerups
        };
        
        // Collection detection
        this.collectionCheckInterval = 100; // Check every 100ms
        this.lastCollectionCheck = 0;
        
        // Bind methods
        this.update = this.update.bind(this);
        this.spawnPowerup = this.spawnPowerup.bind(this);
        this.createPowerupElement = this.createPowerupElement.bind(this);
        this.checkCollections = this.checkCollections.bind(this);
        this.collectPowerup = this.collectPowerup.bind(this);
        this.applyPowerupEffect = this.applyPowerupEffect.bind(this);
    }
    
    /**
     * Initialize powerup system
     */
    init() {
        // Listen for block destruction events
        this.gameState.on('cellChanged', (data) => {
            if (data.value === 0) { // Cell became empty (block destroyed)
                this.onBlockDestroyed(data.x, data.y);
            }
        });
        
        console.log('Powerup system initialized');
    }
    
    /**
     * Update powerup system
     */
    update(deltaTime) {
        const currentTime = Date.now();
        
        // Check for powerup collections periodically
        if (currentTime - this.lastCollectionCheck >= this.collectionCheckInterval) {
            this.checkCollections();
            this.lastCollectionCheck = currentTime;
        }
        
        // Update powerup visuals (glowing animation)
        for (const powerup of this.gameState.powerups.values()) {
            this.updatePowerupVisual(powerup, currentTime);
        }
    }
    
    /**
     * Handle block destruction
     */
    onBlockDestroyed(gridX, gridY) {
        // Check if powerup should spawn
        if (Math.random() < this.spawnChance) {
            this.spawnPowerup(gridX, gridY);
        }
    }
    
    /**
     * Spawn a powerup at grid position
     */
    spawnPowerup(gridX, gridY, type = null) {
        // Determine powerup type if not specified
        if (!type) {
            type = this.getRandomPowerupType();
        }
        
        // Calculate pixel position (center of cell)
        const pixelX = gridX * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2;
        const pixelY = gridY * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2;
        
        // Create powerup
        const powerup = this.gameState.addPowerup({
            type: type,
            x: pixelX,
            y: pixelY
        });
        
        // Create visual element
        this.createPowerupElement(powerup);
        
        console.log(`Powerup ${type} spawned at (${gridX}, ${gridY})`);
        return powerup;
    }
    
    /**
     * Get random powerup type based on weights
     */
    getRandomPowerupType() {
        const random = Math.random();
        let cumulative = 0;
        
        for (const [type, weight] of Object.entries(this.spawnWeights)) {
            cumulative += weight;
            if (random <= cumulative) {
                return type;
            }
        }
        
        // Fallback to bomb if something goes wrong
        return 'bomb';
    }
    
    /**
     * Create powerup DOM element
     */
    createPowerupElement(powerup) {
        const container = DOM.query('.game-grid');
        if (!container) return;
        
        const powerupInfo = this.powerupTypes[powerup.type];
        
        const element = DOM.createElement('div', {
            className: `powerup powerup-${powerup.type}`,
            title: `${powerupInfo.name}: ${powerupInfo.description}`,
            style: {
                position: 'absolute',
                left: powerup.x + 'px',
                top: powerup.y + 'px',
                width: '30px',
                height: '30px',
                backgroundColor: powerupInfo.color,
                border: '2px solid #fff',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                zIndex: '6',
                cursor: 'pointer'
            }
        }, [powerupInfo.icon]);
        
        container.appendChild(element);
        powerup.element = element;
        
        return element;
    }
    
    /**
     * Update powerup visual (glowing animation)
     */
    updatePowerupVisual(powerup, currentTime) {
        if (!powerup.element) return;
        
        // Glowing effect
        const glowIntensity = 0.5 + 0.5 * Math.sin(currentTime * 0.005);
        const shadowSize = 5 + glowIntensity * 10;
        
        const powerupInfo = this.powerupTypes[powerup.type];
        powerup.element.style.boxShadow = `0 0 ${shadowSize}px ${powerupInfo.color}`;
        
        // Slight floating animation
        const floatOffset = Math.sin(currentTime * 0.003) * 2;
        powerup.element.style.transform = `translateY(${floatOffset}px)`;
    }
    
    /**
     * Check for powerup collections
     */
    checkCollections() {
        if (!this.gameState.currentPlayer || !this.gameState.currentPlayer.isAlive) {
            return;
        }
        
        const player = this.gameState.currentPlayer;
        const playerGridX = Math.floor((player.x + 18) / this.gameState.settings.cellSize); // Center of player
        const playerGridY = Math.floor((player.y + 18) / this.gameState.settings.cellSize);
        
        // Check all powerups
        for (const [powerupId, powerup] of this.gameState.powerups) {
            const powerupGridX = Math.floor((powerup.x + 15) / this.gameState.settings.cellSize); // Center of powerup
            const powerupGridY = Math.floor((powerup.y + 15) / this.gameState.settings.cellSize);
            
            // Check if player is on the same cell as powerup
            if (playerGridX === powerupGridX && playerGridY === powerupGridY) {
                this.collectPowerup(player, powerup);
                break; // Only collect one powerup per check
            }
        }
    }
    
    /**
     * Collect powerup
     */
    collectPowerup(player, powerup) {
        console.log(`Player ${player.nickname} collected ${powerup.type} powerup`);
        
        // Apply powerup effect
        this.applyPowerupEffect(player, powerup.type);
        
        // Create collection effect
        this.createCollectionEffect(powerup);
        
        // Remove powerup
        this.removePowerup(powerup.id);
        
        // Emit collection event
        this.gameState.emit('powerupCollected', { player, powerup });
    }
    
    /**
     * Apply powerup effect to player
     */
    applyPowerupEffect(player, type) {
        const powerupInfo = this.powerupTypes[type];
        
        switch (type) {
            case 'bomb':
                player.powerups.bombs++;
                console.log(`${player.nickname} bomb capacity: ${player.powerups.bombs}`);
                break;
                
            case 'flame':
                player.powerups.flames++;
                console.log(`${player.nickname} flame power: ${player.powerups.flames}`);
                break;
                
            case 'speed':
                player.powerups.speed++;
                console.log(`${player.nickname} speed level: ${player.powerups.speed}`);
                break;
        }
        
        // Update player UI
        this.gameState.emit('playerUpdated', player);
        
        // Show collection notification
        this.showCollectionNotification(player, powerupInfo);
    }
    
    /**
     * Create visual effect when powerup is collected
     */
    createCollectionEffect(powerup) {
        if (!powerup.element) return;
        
        const effect = DOM.createElement('div', {
            className: 'powerup-collection-effect',
            style: {
                position: 'absolute',
                left: powerup.x + 'px',
                top: powerup.y + 'px',
                width: '30px',
                height: '30px',
                border: '2px solid #fff',
                borderRadius: '50%',
                zIndex: '10',
                animation: 'powerup-collect 0.5s ease-out forwards'
            }
        });
        
        // Add CSS animation if not already added
        if (!document.querySelector('#powerup-animations')) {
            const style = DOM.createElement('style', { id: 'powerup-animations' }, [`
                @keyframes powerup-collect {
                    0% { transform: scale(1); opacity: 1; }
                    100% { transform: scale(2); opacity: 0; }
                }
            `]);
            document.head.appendChild(style);
        }
        
        const container = DOM.query('.game-grid');
        if (container) {
            container.appendChild(effect);
            
            // Remove effect after animation
            setTimeout(() => {
                DOM.remove(effect);
            }, 500);
        }
    }
    
    /**
     * Show collection notification
     */
    showCollectionNotification(player, powerupInfo) {
        // This could be expanded to show a UI notification
        // For now, just log to console
        console.log(`${player.nickname} gained ${powerupInfo.name}! ${powerupInfo.description}`);
    }
    
    /**
     * Remove powerup
     */
    removePowerup(powerupId) {
        const powerup = this.gameState.powerups.get(powerupId);
        if (powerup && powerup.element) {
            DOM.remove(powerup.element);
        }
        
        this.gameState.removePowerup(powerupId);
    }
    
    /**
     * Get powerup info
     */
    getPowerupInfo(type) {
        return this.powerupTypes[type] || null;
    }
    
    /**
     * Get all powerup types
     */
    getAllPowerupTypes() {
        return Object.keys(this.powerupTypes);
    }
    
    /**
     * Set spawn chance
     */
    setSpawnChance(chance) {
        this.spawnChance = Math.max(0, Math.min(1, chance));
    }
    
    /**
     * Set spawn weights
     */
    setSpawnWeights(weights) {
        // Normalize weights to sum to 1
        const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
        
        if (total > 0) {
            for (const [type, weight] of Object.entries(weights)) {
                if (this.powerupTypes[type]) {
                    this.spawnWeights[type] = weight / total;
                }
            }
        }
    }
    
    /**
     * Cleanup powerup system
     */
    cleanup() {
        // Remove all powerup elements
        for (const powerup of this.gameState.powerups.values()) {
            if (powerup.element) {
                DOM.remove(powerup.element);
            }
        }
        
        // Remove animation styles
        const animationStyle = document.querySelector('#powerup-animations');
        if (animationStyle) {
            DOM.remove(animationStyle);
        }
    }
}

// Export for use in other modules
window.PowerupSystem = PowerupSystem;
