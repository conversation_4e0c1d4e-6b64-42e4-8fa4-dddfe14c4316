<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM - Debug</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <div style="color: white; padding: 20px;">
            <h1>Loading Bomberman DOM...</h1>
            <div id="loading-status"></div>
        </div>
    </div>
    
    <script>
        console.log('Debug page starting...');
        
        const status = document.getElementById('loading-status');
        
        function addStatus(message, isError = false) {
            const div = document.createElement('div');
            div.style.color = isError ? '#ff4444' : '#00ff00';
            div.style.margin = '5px 0';
            div.textContent = message;
            status.appendChild(div);
            console.log(message);
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    addStatus(`✓ Loaded: ${src}`);
                    resolve();
                };
                script.onerror = () => {
                    addStatus(`✗ Failed to load: ${src}`, true);
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }
        
        async function loadAllScripts() {
            try {
                addStatus('Starting script loading...');
                
                // Load framework
                await loadScript('framework/dom.js');
                await loadScript('framework/component.js');
                await loadScript('framework/game-engine.js');
                
                // Test framework
                if (typeof DOM !== 'undefined') {
                    addStatus('✓ DOM framework available');
                } else {
                    addStatus('✗ DOM framework not available', true);
                }
                
                if (typeof Component !== 'undefined') {
                    addStatus('✓ Component framework available');
                } else {
                    addStatus('✗ Component framework not available', true);
                }
                
                if (typeof GameEngine !== 'undefined') {
                    addStatus('✓ GameEngine available');
                } else {
                    addStatus('✗ GameEngine not available', true);
                }
                
                // Load game core
                await loadScript('game/core/game-state.js');
                await loadScript('game/core/performance.js');
                await loadScript('game/core/input.js');
                
                // Load game systems
                await loadScript('game/systems/player.js');
                await loadScript('game/systems/map.js');
                await loadScript('game/systems/bomb.js');
                await loadScript('game/systems/powerup.js');
                
                // Load game components
                await loadScript('game/components/lobby.js');
                await loadScript('game/components/game-board.js');
                await loadScript('game/components/chat.js');
                await loadScript('game/components/hud.js');
                
                // Load networking
                await loadScript('game/network/websocket-client.js');
                
                // Load main game
                await loadScript('game/main.js');
                
                addStatus('✓ All scripts loaded successfully!');
                
                // Test if main classes are available
                if (typeof BombermanGame !== 'undefined') {
                    addStatus('✓ BombermanGame class available');
                    
                    // Try to initialize the game
                    addStatus('Attempting to initialize game...');
                    
                    try {
                        window.game = new BombermanGame();
                        addStatus('✓ Game instance created');
                        
                        window.game.init();
                        addStatus('✓ Game initialized successfully!');
                        
                    } catch (error) {
                        addStatus(`✗ Game initialization failed: ${error.message}`, true);
                        console.error('Game initialization error:', error);
                    }
                } else {
                    addStatus('✗ BombermanGame class not available', true);
                }
                
            } catch (error) {
                addStatus(`✗ Script loading failed: ${error.message}`, true);
                console.error('Script loading error:', error);
            }
        }
        
        // Start loading
        loadAllScripts();
    </script>
</body>
</html>
