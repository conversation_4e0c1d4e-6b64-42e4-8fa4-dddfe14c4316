/* Bomberman DOM - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

#app {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Game Board */
.game-board {
    position: relative;
    width: 800px;
    height: 600px;
    margin: 50px auto;
    background: #2d5a2d;
    border: 4px solid #8B4513;
    border-radius: 8px;
    overflow: hidden;
}

.game-grid {
    position: relative;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(11, 1fr);
}

/* Game Entities */
.player {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #fff;
    transition: none;
    z-index: 10;
}

.player-1 { background: #ff4444; }
.player-2 { background: #4444ff; }
.player-3 { background: #44ff44; }
.player-4 { background: #ffff44; }

.bomb {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #333;
    border: 2px solid #666;
    border-radius: 50%;
    z-index: 5;
    animation: bomb-pulse 0.5s infinite alternate;
}

@keyframes bomb-pulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

.explosion {
    position: absolute;
    background: #ff6600;
    z-index: 8;
    animation: explosion-fade 0.5s ease-out forwards;
}

@keyframes explosion-fade {
    0% { opacity: 1; transform: scale(0.8); }
    100% { opacity: 0; transform: scale(1.2); }
}

.wall {
    background: #8B4513;
    border: 1px solid #654321;
}

.block {
    background: #CD853F;
    border: 1px solid #A0522D;
}

.powerup {
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 2px solid #fff;
    z-index: 6;
    animation: powerup-glow 1s infinite alternate;
}

@keyframes powerup-glow {
    0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
    100% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.8); }
}

.powerup-bomb { background: #ff0000; }
.powerup-flame { background: #ff6600; }
.powerup-speed { background: #00ff00; }

/* UI Components */
.lobby {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    min-width: 400px;
}

.lobby h1 {
    color: #ff6600;
    margin-bottom: 30px;
    font-size: 2.5em;
}

.lobby input {
    padding: 10px;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    margin: 10px;
    width: 200px;
}

.lobby button {
    padding: 10px 20px;
    font-size: 16px;
    background: #ff6600;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px;
}

.lobby button:hover {
    background: #e55a00;
}

.player-counter {
    font-size: 1.5em;
    margin: 20px 0;
}

.timer {
    font-size: 2em;
    color: #ff4444;
    margin: 20px 0;
}

/* HUD */
.hud {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    height: 60px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 100;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.lives {
    color: #ff4444;
    font-weight: bold;
}

.powerups {
    display: flex;
    gap: 5px;
}

.powerup-icon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    border: 1px solid #fff;
}

/* Chat */
.chat {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 300px;
    height: 200px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-size: 12px;
}

.chat-input {
    display: flex;
    padding: 10px;
}

.chat-input input {
    flex: 1;
    padding: 5px;
    border: none;
    border-radius: 3px;
    font-size: 12px;
}

.chat-input button {
    padding: 5px 10px;
    margin-left: 5px;
    background: #ff6600;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

/* Performance Monitor */
.performance {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 1000;
}

/* Game Controls Info */
.controls-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    font-size: 11px;
    color: #ccc;
    z-index: 100;
}

.controls-info h4 {
    margin: 0 0 5px 0;
    color: #ff6600;
}

.controls-info div {
    margin: 2px 0;
}

/* Loading screen */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #ff6600;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Game over screen */
.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    z-index: 1000;
}

.game-over h1 {
    color: #ff6600;
    margin-bottom: 20px;
    font-size: 2.5em;
}

.game-over .winner {
    color: #00ff00;
    font-size: 1.5em;
    margin: 20px 0;
}

.game-over button {
    padding: 10px 20px;
    font-size: 16px;
    background: #ff6600;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px;
}

/* Responsive */
@media (max-width: 900px) {
    .game-board {
        width: 90vw;
        height: 67.5vw;
        margin: 20px auto;
    }

    .chat {
        width: 250px;
        height: 150px;
    }

    .controls-info {
        display: none;
    }
}
