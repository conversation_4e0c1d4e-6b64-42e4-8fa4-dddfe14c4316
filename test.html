<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman Test</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #004400;
            border: 1px solid #00ff00;
        }
        .error {
            background: #440000;
            border: 1px solid #ff0000;
        }
    </style>
</head>
<body>
    <h1>Bomberman DOM - Debug Test</h1>
    <div id="test-results"></div>
    
    <script>
        console.log('Test page loaded');
        
        const results = document.getElementById('test-results');
        
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            results.appendChild(div);
            console.log(message);
        }
        
        // Test basic functionality
        addResult('✓ Basic JavaScript working');
        
        // Test if we can access the app div
        const app = document.getElementById('app');
        if (app) {
            addResult('✓ App div found');
        } else {
            addResult('✗ App div not found', false);
        }
        
        // Test DOM creation
        try {
            const testDiv = document.createElement('div');
            testDiv.textContent = 'Test';
            addResult('✓ DOM manipulation working');
        } catch (error) {
            addResult('✗ DOM manipulation failed: ' + error.message, false);
        }
        
        // Test WebSocket availability
        if (typeof WebSocket !== 'undefined') {
            addResult('✓ WebSocket available');
        } else {
            addResult('✗ WebSocket not available', false);
        }
        
        // Test if we can connect to WebSocket
        try {
            const ws = new WebSocket('ws://localhost:8080/ws');
            ws.onopen = () => {
                addResult('✓ WebSocket connection successful');
                ws.close();
            };
            ws.onerror = (error) => {
                addResult('✗ WebSocket connection failed', false);
            };
        } catch (error) {
            addResult('✗ WebSocket test failed: ' + error.message, false);
        }
    </script>
    
    <div id="app">
        <!-- This is where the game should load -->
    </div>
</body>
</html>
