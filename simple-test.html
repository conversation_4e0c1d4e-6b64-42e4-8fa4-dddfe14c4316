<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .log {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.1);
        }
        .error {
            background: rgba(255, 0, 0, 0.3);
        }
        .success {
            background: rgba(0, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <h1>Simple Test - Load Main Game Only</h1>
    <div id="logs"></div>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        // Override console.error to catch errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Catch unhandled errors
        window.addEventListener('error', (event) => {
            log(`UNHANDLED ERROR: ${event.error.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        async function loadScript(src) {
            return new Promise((resolve, reject) => {
                log(`Loading ${src}...`);
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    log(`✓ Loaded ${src}`, 'success');
                    resolve();
                };
                script.onerror = () => {
                    log(`✗ Failed to load ${src}`, 'error');
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }
        
        async function test() {
            try {
                log('Starting test...');
                
                // Load just the main game file to see what happens
                await loadScript('game/main.js');
                
                log('✓ Main game file loaded successfully!', 'success');
                
                // Try to create an instance
                if (typeof BombermanGame !== 'undefined') {
                    log('BombermanGame class is available');

                    // Try to create instance
                    log('Creating BombermanGame instance...');
                    const game = new BombermanGame();
                    log('✓ BombermanGame instance created successfully!', 'success');
                } else {
                    log('BombermanGame class not found', 'error');
                }
                
            } catch (error) {
                log(`Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
        
        test();
    </script>
</body>
</html>
