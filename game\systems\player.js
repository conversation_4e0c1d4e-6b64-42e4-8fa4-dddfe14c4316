/**
 * Player System
 * Handles player movement, collision, and game logic
 */

class PlayerSystem {
    constructor(gameState, inputManager, networkClient) {
        this.gameState = gameState;
        this.inputManager = inputManager;
        this.networkClient = networkClient;
        
        // Movement settings
        this.baseSpeed = 2; // pixels per frame at 60fps
        this.speedMultiplier = 1.5; // speed powerup multiplier
        
        // Collision settings
        this.playerSize = 36; // slightly smaller than cell for better movement
        this.collisionPadding = 2;
        
        // Network throttling
        this.lastNetworkUpdate = 0;
        this.networkUpdateInterval = 50; // 20 updates per second
        
        // Bind methods
        this.update = this.update.bind(this);
        this.updatePlayer = this.updatePlayer.bind(this);
        this.handleInput = this.handleInput.bind(this);
        this.movePlayer = this.movePlayer.bind(this);
        this.canMoveTo = this.canMoveTo.bind(this);
        this.placeBomb = this.placeBomb.bind(this);
    }
    
    /**
     * Initialize player system
     */
    init() {
        console.log('Player system initialized');
    }
    
    /**
     * Update player system
     */
    update(deltaTime) {
        // Update all players
        for (const player of this.gameState.getAllPlayers()) {
            this.updatePlayer(player, deltaTime);
        }
        
        // Handle input for current player
        if (this.gameState.currentPlayer && this.gameState.state === 'playing') {
            this.handleInput(this.gameState.currentPlayer, deltaTime);
        }
    }
    
    /**
     * Update individual player
     */
    updatePlayer(player, deltaTime) {
        if (!player.isAlive) return;
        
        // Update player element position if it exists
        if (player.element) {
            DOM.setPosition(player.element, player.x, player.y);
        }
    }
    
    /**
     * Handle input for current player
     */
    handleInput(player, deltaTime) {
        const input = this.inputManager.getPlayerInput('player1');
        if (!input) return;
        
        // Calculate movement
        let moveX = 0;
        let moveY = 0;
        
        if (input.moveX !== 0 || input.moveY !== 0) {
            // Calculate speed based on powerups
            const speed = this.baseSpeed * Math.pow(this.speedMultiplier, player.powerups.speed - 1);
            
            // Normalize diagonal movement
            const magnitude = Math.sqrt(input.moveX * input.moveX + input.moveY * input.moveY);
            if (magnitude > 0) {
                moveX = (input.moveX / magnitude) * speed;
                moveY = (input.moveY / magnitude) * speed;
            }
            
            // Move player
            this.movePlayer(player, moveX, moveY);
        }
        
        // Handle bomb placement
        if (input.bomb) {
            this.placeBomb(player);
        }
    }
    
    /**
     * Move player with collision detection
     */
    movePlayer(player, deltaX, deltaY) {
        const newX = player.x + deltaX;
        const newY = player.y + deltaY;
        
        // Check X movement
        if (this.canMoveTo(player, newX, player.y)) {
            player.x = newX;
        }
        
        // Check Y movement
        if (this.canMoveTo(player, player.x, newY)) {
            player.y = newY;
        }
        
        // Clamp to game bounds
        const maxX = (this.gameState.settings.mapWidth * this.gameState.settings.cellSize) - this.playerSize;
        const maxY = (this.gameState.settings.mapHeight * this.gameState.settings.cellSize) - this.playerSize;
        
        player.x = Math.max(0, Math.min(maxX, player.x));
        player.y = Math.max(0, Math.min(maxY, player.y));
        
        // Send position update to server (throttled)
        const now = Date.now();
        if (now - this.lastNetworkUpdate > this.networkUpdateInterval) {
            this.networkClient.sendPlayerMove(player.x, player.y);
            this.lastNetworkUpdate = now;
        }
    }
    
    /**
     * Check if player can move to position
     */
    canMoveTo(player, x, y) {
        // Get player bounds
        const bounds = {
            left: x + this.collisionPadding,
            right: x + this.playerSize - this.collisionPadding,
            top: y + this.collisionPadding,
            bottom: y + this.playerSize - this.collisionPadding
        };
        
        // Convert to grid coordinates
        const gridLeft = Math.floor(bounds.left / this.gameState.settings.cellSize);
        const gridRight = Math.floor(bounds.right / this.gameState.settings.cellSize);
        const gridTop = Math.floor(bounds.top / this.gameState.settings.cellSize);
        const gridBottom = Math.floor(bounds.bottom / this.gameState.settings.cellSize);
        
        // Check all cells the player would occupy
        for (let gridY = gridTop; gridY <= gridBottom; gridY++) {
            for (let gridX = gridLeft; gridX <= gridRight; gridX++) {
                // Check map collision
                if (!this.gameState.isWalkable(gridX, gridY)) {
                    return false;
                }
                
                // Check bomb collision
                if (this.isBombAt(gridX, gridY)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Check if there's a bomb at grid position
     */
    isBombAt(gridX, gridY) {
        for (const bomb of this.gameState.bombs.values()) {
            const bombGridX = Math.floor(bomb.x / this.gameState.settings.cellSize);
            const bombGridY = Math.floor(bomb.y / this.gameState.settings.cellSize);
            
            if (bombGridX === gridX && bombGridY === gridY) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Place bomb at player position
     */
    placeBomb(player) {
        // Check if player can place more bombs
        if (player.activeBombs >= player.powerups.bombs) {
            return false;
        }
        
        // Calculate bomb position (center of current cell)
        const gridX = Math.floor((player.x + this.playerSize / 2) / this.gameState.settings.cellSize);
        const gridY = Math.floor((player.y + this.playerSize / 2) / this.gameState.settings.cellSize);
        
        // Check if there's already a bomb at this position
        if (this.isBombAt(gridX, gridY)) {
            return false;
        }
        
        // Calculate pixel position for bomb
        const bombX = gridX * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2;
        const bombY = gridY * this.gameState.settings.cellSize + (this.gameState.settings.cellSize - 30) / 2;
        
        // Send bomb placement to server
        this.networkClient.sendPlaceBomb(bombX, bombY);
        
        return true;
    }
    
    /**
     * Create player element
     */
    createPlayerElement(player, container) {
        const element = DOM.createElement('div', {
            className: `player player-${this.getPlayerNumber(player.id)}`,
            style: {
                width: this.playerSize + 'px',
                height: this.playerSize + 'px',
                position: 'absolute',
                transform: `translate(${player.x}px, ${player.y}px)`
            }
        });
        
        // Add player nickname
        const nameElement = DOM.createElement('div', {
            className: 'player-name',
            style: {
                position: 'absolute',
                top: '-20px',
                left: '50%',
                transform: 'translateX(-50%)',
                fontSize: '10px',
                fontWeight: 'bold',
                textAlign: 'center',
                whiteSpace: 'nowrap',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
            }
        }, [player.nickname]);
        
        element.appendChild(nameElement);
        container.appendChild(element);
        
        // Store reference
        player.element = element;
        
        return element;
    }
    
    /**
     * Remove player element
     */
    removePlayerElement(player) {
        if (player.element) {
            DOM.remove(player.element);
            player.element = null;
        }
    }
    
    /**
     * Get player number for styling
     */
    getPlayerNumber(playerId) {
        const players = Array.from(this.gameState.players.keys());
        const index = players.indexOf(playerId);
        return (index % 4) + 1;
    }
    
    /**
     * Handle player taking damage
     */
    takeDamage(player) {
        if (!player.isAlive) return;
        
        player.lives--;
        
        if (player.lives <= 0) {
            this.killPlayer(player);
        }
        
        // Update UI
        this.gameState.emit('playerDamaged', player);
    }
    
    /**
     * Kill player
     */
    killPlayer(player) {
        player.isAlive = false;
        
        // Hide player element
        if (player.element) {
            DOM.addClass(player.element, 'dead');
            player.element.style.opacity = '0.3';
        }
        
        // Check for game end
        const alivePlayers = this.gameState.getAlivePlayers();
        if (alivePlayers.length <= 1) {
            this.gameState.emit('gameEnd', alivePlayers[0] || null);
        }
        
        this.gameState.emit('playerKilled', player);
    }
    
    /**
     * Collect powerup
     */
    collectPowerup(player, powerup) {
        switch (powerup.type) {
            case 'bomb':
                player.powerups.bombs++;
                break;
            case 'flame':
                player.powerups.flames++;
                break;
            case 'speed':
                player.powerups.speed++;
                break;
        }
        
        this.gameState.emit('powerupCollected', { player, powerup });
    }
    
    /**
     * Check powerup collision for player
     */
    checkPowerupCollision(player) {
        const playerGridX = Math.floor((player.x + this.playerSize / 2) / this.gameState.settings.cellSize);
        const playerGridY = Math.floor((player.y + this.playerSize / 2) / this.gameState.settings.cellSize);
        
        for (const powerup of this.gameState.powerups.values()) {
            const powerupGridX = Math.floor(powerup.x / this.gameState.settings.cellSize);
            const powerupGridY = Math.floor(powerup.y / this.gameState.settings.cellSize);
            
            if (playerGridX === powerupGridX && playerGridY === powerupGridY) {
                this.collectPowerup(player, powerup);
                this.gameState.removePowerup(powerup.id);
                return powerup;
            }
        }
        
        return null;
    }
    
    /**
     * Cleanup
     */
    cleanup() {
        // Remove all player elements
        for (const player of this.gameState.getAllPlayers()) {
            this.removePlayerElement(player);
        }
    }
}

// Export for use in other modules
window.PlayerSystem = PlayerSystem;
