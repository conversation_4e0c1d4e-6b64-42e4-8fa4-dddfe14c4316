/**
 * <PERSON>bby Component
 * Handles nickname entry, player counter, and game start countdown
 */

class LobbyComponent extends Component {
    constructor(container, props = {}) {
        super(container, props);
        
        this.state = {
            nickname: '',
            playerCount: 0,
            maxPlayers: 4,
            isJoined: false,
            isCountdown: false,
            countdownTime: 0,
            error: null
        };
        
        // Bind methods
        this.handleNicknameChange = this.handleNicknameChange.bind(this);
        this.handleJoinGame = this.handleJoinGame.bind(this);
        this.handleNetworkMessage = this.handleNetworkMessage.bind(this);
        this.updateCountdown = this.updateCountdown.bind(this);
    }
    
    /**
     * Render lobby component
     */
    render() {
        this.element = DOM.createElement('div', {
            className: 'lobby'
        });
        
        this.updateContent();
        return this.element;
    }
    
    /**
     * Update lobby content based on state
     */
    updateContent() {
        if (!this.element) return;
        
        DOM.clear(this.element);
        
        // Title
        const title = DOM.createElement('h1', {}, ['Bomberman DOM']);
        this.element.appendChild(title);
        
        if (this.state.error) {
            // Error message
            const errorElement = DOM.createElement('div', {
                className: 'error',
                style: {
                    color: '#ff4444',
                    margin: '20px 0',
                    padding: '10px',
                    border: '1px solid #ff4444',
                    borderRadius: '5px'
                }
            }, [this.state.error]);
            this.element.appendChild(errorElement);
        }
        
        if (!this.state.isJoined) {
            // Nickname input form
            this.renderNicknameForm();
        } else if (this.state.isCountdown) {
            // Countdown display
            this.renderCountdown();
        } else {
            // Waiting room
            this.renderWaitingRoom();
        }
    }
    
    /**
     * Render nickname input form
     */
    renderNicknameForm() {
        const form = DOM.createElement('div', {
            className: 'nickname-form'
        });
        
        const instruction = DOM.createElement('p', {}, ['Enter your nickname to join the game:']);
        form.appendChild(instruction);
        
        const input = DOM.createElement('input', {
            type: 'text',
            placeholder: 'Your nickname',
            maxlength: '20',
            value: this.state.nickname,
            onInput: this.handleNicknameChange
        });
        form.appendChild(input);
        
        const button = DOM.createElement('button', {
            onClick: this.handleJoinGame,
            disabled: this.state.nickname.trim().length < 2
        }, ['Join Game']);
        form.appendChild(button);
        
        this.element.appendChild(form);
        
        // Focus input
        setTimeout(() => input.focus(), 100);
    }
    
    /**
     * Render waiting room
     */
    renderWaitingRoom() {
        const waitingRoom = DOM.createElement('div', {
            className: 'waiting-room'
        });
        
        const status = DOM.createElement('h2', {}, ['Waiting for players...']);
        waitingRoom.appendChild(status);
        
        const counter = DOM.createElement('div', {
            className: 'player-counter'
        }, [`Players: ${this.state.playerCount}/${this.state.maxPlayers}`]);
        waitingRoom.appendChild(counter);
        
        // Progress bar
        const progressContainer = DOM.createElement('div', {
            style: {
                width: '300px',
                height: '20px',
                background: '#333',
                borderRadius: '10px',
                margin: '20px auto',
                overflow: 'hidden'
            }
        });
        
        const progressBar = DOM.createElement('div', {
            style: {
                width: `${(this.state.playerCount / this.state.maxPlayers) * 100}%`,
                height: '100%',
                background: '#ff6600',
                transition: 'width 0.3s ease'
            }
        });
        
        progressContainer.appendChild(progressBar);
        waitingRoom.appendChild(progressContainer);
        
        // Instructions
        const instructions = DOM.createElement('div', {
            className: 'instructions',
            style: {
                fontSize: '14px',
                color: '#ccc',
                margin: '20px 0',
                lineHeight: '1.5'
            }
        });
        
        if (this.state.playerCount < 2) {
            instructions.innerHTML = 'Waiting for at least 2 players to join...';
        } else if (this.state.playerCount < this.state.maxPlayers) {
            instructions.innerHTML = 'Game will start in 20 seconds or when 4 players join.<br>Get ready!';
        } else {
            instructions.innerHTML = 'All players joined! Starting countdown...';
        }
        
        waitingRoom.appendChild(instructions);
        this.element.appendChild(waitingRoom);
    }
    
    /**
     * Render countdown
     */
    renderCountdown() {
        const countdown = DOM.createElement('div', {
            className: 'countdown'
        });
        
        const title = DOM.createElement('h2', {}, ['Game Starting!']);
        countdown.appendChild(title);
        
        const timer = DOM.createElement('div', {
            className: 'timer'
        }, [Math.ceil(this.state.countdownTime / 1000).toString()]);
        countdown.appendChild(timer);
        
        const instruction = DOM.createElement('p', {}, ['Get ready to battle!']);
        countdown.appendChild(instruction);
        
        this.element.appendChild(countdown);
    }
    
    /**
     * Handle nickname input change
     */
    handleNicknameChange(event) {
        this.setState({ nickname: event.target.value });
    }
    
    /**
     * Handle join game button click
     */
    handleJoinGame() {
        const nickname = this.state.nickname.trim();
        
        if (nickname.length < 2) {
            this.setState({ error: 'Nickname must be at least 2 characters long' });
            return;
        }
        
        if (nickname.length > 20) {
            this.setState({ error: 'Nickname must be 20 characters or less' });
            return;
        }
        
        // Clear error and attempt to join
        this.setState({ error: null });
        
        // Emit join event
        this.emit('joinGame', { nickname });
    }
    
    /**
     * Handle network messages
     */
    handleNetworkMessage(type, data) {
        switch (type) {
            case 'join_success':
                this.setState({
                    isJoined: true,
                    playerCount: data.playerCount,
                    maxPlayers: data.maxPlayers,
                    error: null
                });
                break;
                
            case 'join_error':
                this.setState({
                    error: data.message || 'Failed to join game',
                    isJoined: false
                });
                break;
                
            case 'player_count_update':
                this.setState({
                    playerCount: data.playerCount,
                    maxPlayers: data.maxPlayers
                });
                break;
                
            case 'countdown_start':
                this.setState({
                    isCountdown: true,
                    countdownTime: data.duration
                });
                this.startCountdownTimer();
                break;
                
            case 'game_start':
                // Game is starting, hide lobby
                this.emit('gameStart', data);
                break;
                
            case 'game_reset':
                this.setState({
                    isJoined: false,
                    isCountdown: false,
                    countdownTime: 0,
                    playerCount: 0,
                    error: null
                });
                break;
        }
    }
    
    /**
     * Start countdown timer
     */
    startCountdownTimer() {
        const startTime = Date.now();
        const duration = this.state.countdownTime;
        
        const updateTimer = () => {
            const elapsed = Date.now() - startTime;
            const remaining = Math.max(0, duration - elapsed);
            
            this.setState({ countdownTime: remaining });
            
            if (remaining > 0) {
                requestAnimationFrame(updateTimer);
            }
        };
        
        updateTimer();
    }
    
    /**
     * Update component state
     */
    update(prevState) {
        this.updateContent();
    }
    
    /**
     * Component mounted
     */
    onMounted() {
        console.log('Lobby component mounted');
    }
    
    /**
     * Component unmounted
     */
    onUnmounted() {
        console.log('Lobby component unmounted');
    }
    
    /**
     * Event system methods
     */
    emit(event, data) {
        if (this.props.onEvent) {
            this.props.onEvent(event, data);
        }
    }
    
    /**
     * Set network client for handling messages
     */
    setNetworkClient(networkClient) {
        this.networkClient = networkClient;
        
        // Register for network events
        if (networkClient) {
            networkClient.on('join_success', (data) => this.handleNetworkMessage('join_success', data));
            networkClient.on('join_error', (data) => this.handleNetworkMessage('join_error', data));
            networkClient.on('player_count_update', (data) => this.handleNetworkMessage('player_count_update', data));
            networkClient.on('countdown_start', (data) => this.handleNetworkMessage('countdown_start', data));
            networkClient.on('game_start', (data) => this.handleNetworkMessage('game_start', data));
            networkClient.on('game_reset', (data) => this.handleNetworkMessage('game_reset', data));
        }
    }
    
    /**
     * Reset lobby to initial state
     */
    reset() {
        this.setState({
            nickname: '',
            playerCount: 0,
            maxPlayers: 4,
            isJoined: false,
            isCountdown: false,
            countdownTime: 0,
            error: null
        });
    }
}

// Export for use in other modules
window.LobbyComponent = LobbyComponent;
