/**
 * WebSocket Client for Bomberman DOM
 * Handles client-side networking and communication with server
 */

class WebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.playerId = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        // Event handlers
        this.handlers = new Map();
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.handleMessage = this.handleMessage.bind(this);
        this.handleOpen = this.handleOpen.bind(this);
        this.handleClose = this.handleClose.bind(this);
        this.handleError = this.handleError.bind(this);
    }
    
    /**
     * Connect to WebSocket server
     */
    connect(url = null) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('Already connected to WebSocket');
            return;
        }
        
        // Determine WebSocket URL
        if (!url) {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            url = `${protocol}//${host}/ws`;
        }
        
        console.log('Connecting to WebSocket:', url);
        
        try {
            this.ws = new WebSocket(url);
            this.ws.onopen = this.handleOpen;
            this.ws.onmessage = this.handleMessage;
            this.ws.onclose = this.handleClose;
            this.ws.onerror = this.handleError;
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.handleError(error);
        }
    }
    
    /**
     * Disconnect from WebSocket server
     */
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.playerId = null;
    }
    
    /**
     * Send message to server
     */
    send(type, data = {}) {
        if (!this.isConnected || !this.ws) {
            console.warn('Cannot send message: not connected');
            return false;
        }
        
        try {
            const message = {
                type: type,
                data: data,
                timestamp: Date.now()
            };
            
            this.ws.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('Failed to send message:', error);
            return false;
        }
    }
    
    /**
     * Handle WebSocket open event
     */
    handleOpen(event) {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Emit connected event
        this.emit('connected');
        
        // Start ping/pong for connection monitoring
        this.startPing();
    }
    
    /**
     * Handle incoming WebSocket messages
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            
            // Handle system messages
            switch (message.type) {
                case 'pong':
                    // Handle ping/pong for connection monitoring
                    break;
                    
                case 'join_success':
                    this.playerId = message.data.playerId;
                    console.log('Joined game as player:', this.playerId);
                    break;
                    
                case 'join_error':
                    console.error('Failed to join game:', message.message);
                    break;
            }
            
            // Emit message to registered handlers
            this.emit(message.type, message.data);
            
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    /**
     * Handle WebSocket close event
     */
    handleClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        
        // Stop ping
        this.stopPing();
        
        // Emit disconnected event
        this.emit('disconnected', { code: event.code, reason: event.reason });
        
        // Attempt reconnection if not intentional
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
    }
    
    /**
     * Handle WebSocket error event
     */
    handleError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }
    
    /**
     * Attempt to reconnect to server
     */
    attemptReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }
    
    /**
     * Start ping/pong for connection monitoring
     */
    startPing() {
        this.pingInterval = setInterval(() => {
            if (this.isConnected) {
                this.send('ping');
            }
        }, 30000); // Ping every 30 seconds
    }
    
    /**
     * Stop ping/pong
     */
    stopPing() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
    }
    
    /**
     * Register event handler
     */
    on(event, handler) {
        if (!this.handlers.has(event)) {
            this.handlers.set(event, []);
        }
        this.handlers.get(event).push(handler);
    }
    
    /**
     * Unregister event handler
     */
    off(event, handler) {
        if (this.handlers.has(event)) {
            const handlers = this.handlers.get(event);
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    /**
     * Emit event to registered handlers
     */
    emit(event, data = null) {
        if (this.handlers.has(event)) {
            this.handlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * Game-specific methods
     */
    
    joinGame(nickname) {
        return this.send('join', { nickname });
    }
    
    sendChatMessage(message) {
        return this.send('chat', { message });
    }
    
    sendPlayerMove(x, y) {
        return this.send('player_move', { x, y });
    }
    
    sendPlaceBomb(x, y) {
        return this.send('place_bomb', { x, y });
    }
    
    /**
     * Get connection status
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            playerId: this.playerId,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

// Export for use in other modules
window.WebSocketClient = WebSocketClient;
