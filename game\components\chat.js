/**
 * Chat Component
 * Real-time chat functionality using WebSockets
 */

class ChatComponent extends Component {
    constructor(container, props = {}) {
        super(container, props);
        
        this.state = {
            messages: [],
            currentMessage: '',
            isVisible: true,
            maxMessages: 50
        };
        
        // Chat settings
        this.maxMessageLength = 200;
        this.messageTimeout = 30000; // 30 seconds before old messages fade
        
        // Bind methods
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleSendMessage = this.handleSendMessage.bind(this);
        this.handleKeyPress = this.handleKeyPress.bind(this);
        this.addMessage = this.addMessage.bind(this);
        this.clearOldMessages = this.clearOldMessages.bind(this);
        this.toggleVisibility = this.toggleVisibility.bind(this);
    }
    
    /**
     * Render chat component
     */
    render() {
        this.element = DOM.createElement('div', {
            className: 'chat',
            style: {
                display: this.state.isVisible ? 'flex' : 'none'
            }
        });
        
        this.createChatElements();
        return this.element;
    }
    
    /**
     * Create chat UI elements
     */
    createChatElements() {
        if (!this.element) return;
        
        DOM.clear(this.element);
        
        // Messages container
        this.messagesContainer = DOM.createElement('div', {
            className: 'chat-messages',
            style: {
                flex: '1',
                overflowY: 'auto',
                padding: '10px',
                fontSize: '12px',
                lineHeight: '1.4'
            }
        });
        
        this.element.appendChild(this.messagesContainer);
        
        // Input container
        const inputContainer = DOM.createElement('div', {
            className: 'chat-input',
            style: {
                display: 'flex',
                padding: '10px',
                borderTop: '1px solid #444'
            }
        });
        
        // Message input
        this.messageInput = DOM.createElement('input', {
            type: 'text',
            placeholder: 'Type a message...',
            maxlength: this.maxMessageLength.toString(),
            value: this.state.currentMessage,
            style: {
                flex: '1',
                padding: '5px',
                border: 'none',
                borderRadius: '3px',
                fontSize: '12px',
                backgroundColor: '#333',
                color: '#fff'
            },
            onInput: this.handleInputChange,
            onKeyPress: this.handleKeyPress
        });
        
        inputContainer.appendChild(this.messageInput);
        
        // Send button
        const sendButton = DOM.createElement('button', {
            onClick: this.handleSendMessage,
            style: {
                padding: '5px 10px',
                marginLeft: '5px',
                backgroundColor: '#ff6600',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '12px'
            }
        }, ['Send']);
        
        inputContainer.appendChild(sendButton);
        this.element.appendChild(inputContainer);
        
        // Render existing messages
        this.renderMessages();
    }
    
    /**
     * Render all messages
     */
    renderMessages() {
        if (!this.messagesContainer) return;
        
        DOM.clear(this.messagesContainer);
        
        this.state.messages.forEach(message => {
            this.renderMessage(message);
        });
        
        // Scroll to bottom
        this.scrollToBottom();
    }
    
    /**
     * Render single message
     */
    renderMessage(message) {
        if (!this.messagesContainer) return;
        
        const messageElement = DOM.createElement('div', {
            className: `chat-message ${message.type || 'user'}`,
            style: {
                marginBottom: '5px',
                padding: '2px 0',
                wordWrap: 'break-word'
            }
        });
        
        // Format message based on type
        let content = '';
        const timestamp = new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        switch (message.type) {
            case 'system':
                content = `<span style="color: #ffff00;">[${timestamp}] ${message.text}</span>`;
                break;
                
            case 'join':
                content = `<span style="color: #00ff00;">[${timestamp}] ${message.nickname} joined the game</span>`;
                break;
                
            case 'leave':
                content = `<span style="color: #ff4444;">[${timestamp}] ${message.nickname} left the game</span>`;
                break;
                
            case 'death':
                content = `<span style="color: #ff8800;">[${timestamp}] ${message.nickname} was eliminated!</span>`;
                break;
                
            case 'user':
            default:
                const nicknameColor = this.getNicknameColor(message.nickname);
                content = `<span style="color: #888;">[${timestamp}]</span> <span style="color: ${nicknameColor}; font-weight: bold;">${message.nickname}:</span> <span style="color: #fff;">${this.escapeHtml(message.text)}</span>`;
                break;
        }
        
        messageElement.innerHTML = content;
        this.messagesContainer.appendChild(messageElement);
    }
    
    /**
     * Get consistent color for nickname
     */
    getNicknameColor(nickname) {
        const colors = ['#ff6666', '#66ff66', '#6666ff', '#ffff66', '#ff66ff', '#66ffff'];
        let hash = 0;
        
        for (let i = 0; i < nickname.length; i++) {
            hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
        }
        
        return colors[Math.abs(hash) % colors.length];
    }
    
    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Handle input change
     */
    handleInputChange(event) {
        this.setState({ currentMessage: event.target.value });
    }
    
    /**
     * Handle key press in input
     */
    handleKeyPress(event) {
        if (event.key === 'Enter') {
            this.handleSendMessage();
        }
    }
    
    /**
     * Handle send message
     */
    handleSendMessage() {
        const message = this.state.currentMessage.trim();
        
        if (message.length === 0) return;
        
        if (message.length > this.maxMessageLength) {
            this.addSystemMessage(`Message too long (max ${this.maxMessageLength} characters)`);
            return;
        }
        
        // Send message through network
        if (this.props.networkClient) {
            this.props.networkClient.sendChatMessage(message);
        }
        
        // Clear input
        this.setState({ currentMessage: '' });
        if (this.messageInput) {
            this.messageInput.value = '';
        }
    }
    
    /**
     * Add message to chat
     */
    addMessage(message) {
        const newMessage = {
            id: Date.now() + Math.random(),
            timestamp: Date.now(),
            ...message
        };
        
        const messages = [...this.state.messages, newMessage];
        
        // Limit number of messages
        if (messages.length > this.state.maxMessages) {
            messages.splice(0, messages.length - this.state.maxMessages);
        }
        
        this.setState({ messages });
        
        // Auto-scroll to bottom
        setTimeout(() => this.scrollToBottom(), 50);
    }
    
    /**
     * Add system message
     */
    addSystemMessage(text) {
        this.addMessage({
            type: 'system',
            text: text
        });
    }
    
    /**
     * Add user message
     */
    addUserMessage(nickname, text) {
        this.addMessage({
            type: 'user',
            nickname: nickname,
            text: text
        });
    }
    
    /**
     * Add join message
     */
    addJoinMessage(nickname) {
        this.addMessage({
            type: 'join',
            nickname: nickname
        });
    }
    
    /**
     * Add leave message
     */
    addLeaveMessage(nickname) {
        this.addMessage({
            type: 'leave',
            nickname: nickname
        });
    }
    
    /**
     * Add death message
     */
    addDeathMessage(nickname) {
        this.addMessage({
            type: 'death',
            nickname: nickname
        });
    }
    
    /**
     * Scroll to bottom of messages
     */
    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }
    
    /**
     * Toggle chat visibility
     */
    toggleVisibility() {
        this.setState({ isVisible: !this.state.isVisible });
    }
    
    /**
     * Show chat
     */
    show() {
        this.setState({ isVisible: true });
    }
    
    /**
     * Hide chat
     */
    hide() {
        this.setState({ isVisible: false });
    }
    
    /**
     * Clear all messages
     */
    clearMessages() {
        this.setState({ messages: [] });
    }
    
    /**
     * Clear old messages (called periodically)
     */
    clearOldMessages() {
        const now = Date.now();
        const messages = this.state.messages.filter(message => 
            now - message.timestamp < this.messageTimeout
        );
        
        if (messages.length !== this.state.messages.length) {
            this.setState({ messages });
        }
    }
    
    /**
     * Handle network messages
     */
    handleNetworkMessage(type, data) {
        switch (type) {
            case 'chat_message':
                this.addUserMessage(data.nickname, data.message);
                break;
                
            case 'player_joined':
                this.addJoinMessage(data.nickname);
                break;
                
            case 'player_left':
                this.addLeaveMessage(data.nickname);
                break;
                
            case 'player_died':
                this.addDeathMessage(data.nickname);
                break;
                
            case 'game_start':
                this.addSystemMessage('Game started! Good luck!');
                break;
                
            case 'game_end':
                if (data.winner) {
                    this.addSystemMessage(`${data.winner.nickname} wins the game!`);
                } else {
                    this.addSystemMessage('Game ended!');
                }
                break;
        }
    }
    
    /**
     * Update component
     */
    update(prevState) {
        if (prevState.isVisible !== this.state.isVisible) {
            this.element.style.display = this.state.isVisible ? 'flex' : 'none';
        }
        
        if (prevState.messages !== this.state.messages) {
            this.renderMessages();
        }
        
        if (prevState.currentMessage !== this.state.currentMessage && this.messageInput) {
            this.messageInput.value = this.state.currentMessage;
        }
    }
    
    /**
     * Component mounted
     */
    onMounted() {
        console.log('Chat component mounted');
        
        // Start periodic cleanup of old messages
        this.cleanupInterval = setInterval(this.clearOldMessages, 60000); // Every minute
        
        // Add welcome message
        this.addSystemMessage('Welcome to Bomberman! Use chat to communicate with other players.');
    }
    
    /**
     * Component unmounted
     */
    onUnmounted() {
        console.log('Chat component unmounted');
        
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
    }
    
    /**
     * Set network client
     */
    setNetworkClient(networkClient) {
        this.props.networkClient = networkClient;
        
        if (networkClient) {
            // Register for chat events
            networkClient.on('chat_message', (data) => this.handleNetworkMessage('chat_message', data));
        }
    }
}

// Export for use in other modules
window.ChatComponent = ChatComponent;
